import torch
import torch.nn as nn
import torch.nn.functional as F
from opt_einsum import contract

class FlowHead(nn.Module):
    def __init__(self, input_dim=128, hidden_dim=256, output_dim=2):
        super(FlowHead, self).__init__()
        self.conv1 = nn.Conv2d(input_dim, hidden_dim, 3, padding=1)
        self.conv2 = nn.Conv2d(hidden_dim, output_dim, 3, padding=1)
        self.relu = nn.ReLU(inplace=True)

    def forward(self, x):
        return self.conv2(self.relu(self.conv1(x)))

class DispHead(nn.Module):
    def __init__(self, input_dim=128, hidden_dim=256, output_dim=1):
        super(DispHead, self).__init__()
        self.conv1 = nn.Conv2d(input_dim, hidden_dim, 3, padding=1)
        self.conv2 = nn.Conv2d(hidden_dim, output_dim, 7, padding=3)
        self.relu = nn.GELU() #nn.ReLU()

    def forward(self, x):
        return self.conv2(self.relu(self.conv1(x)))

class ConvGRU(nn.Module):
    def __init__(self, hidden_dim, input_dim, kernel_size=3):
        super(ConvGRU, self).__init__()
        self.convz = nn.Conv2d(hidden_dim+input_dim, hidden_dim, kernel_size, padding=kernel_size//2)
        self.convr = nn.Conv2d(hidden_dim+input_dim, hidden_dim, kernel_size, padding=kernel_size//2)
        self.convq = nn.Conv2d(hidden_dim+input_dim, hidden_dim, kernel_size, padding=kernel_size//2)

    def forward(self, h, cz, cr, cq, *x_list):

        x = torch.cat(x_list, dim=1)
        hx = torch.cat([h, x], dim=1)
        z = torch.sigmoid(self.convz(hx) + cz)
        r = torch.sigmoid(self.convr(hx) + cr)
        q = torch.tanh(self.convq(torch.cat([r*h, x], dim=1)) + cq)
        h = (1-z) * h + z * q
        return h

class SepConvGRU(nn.Module):
    def __init__(self, hidden_dim=128, input_dim=192+128):
        super(SepConvGRU, self).__init__()
        self.convz1 = nn.Conv2d(hidden_dim+input_dim, hidden_dim, (1,5), padding=(0,2))
        self.convr1 = nn.Conv2d(hidden_dim+input_dim, hidden_dim, (1,5), padding=(0,2))
        self.convq1 = nn.Conv2d(hidden_dim+input_dim, hidden_dim, (1,5), padding=(0,2))

        self.convz2 = nn.Conv2d(hidden_dim+input_dim, hidden_dim, (5,1), padding=(2,0))
        self.convr2 = nn.Conv2d(hidden_dim+input_dim, hidden_dim, (5,1), padding=(2,0))
        self.convq2 = nn.Conv2d(hidden_dim+input_dim, hidden_dim, (5,1), padding=(2,0))


    def forward(self, h, *x):
        # horizontal
        x = torch.cat(x, dim=1)
        hx = torch.cat([h, x], dim=1)
        z = torch.sigmoid(self.convz1(hx))
        r = torch.sigmoid(self.convr1(hx))
        q = torch.tanh(self.convq1(torch.cat([r*h, x], dim=1)))        
        h = (1-z) * h + z * q

        # vertical
        hx = torch.cat([h, x], dim=1)
        z = torch.sigmoid(self.convz2(hx))
        r = torch.sigmoid(self.convr2(hx))
        q = torch.tanh(self.convq2(torch.cat([r*h, x], dim=1)))       
        h = (1-z) * h + z * q

        return h

def interp(x, dest):
    original_dtype = x.dtype
    x_fp32 = x.float()
    interp_args = {'mode': 'bilinear', 'align_corners': True}
    with torch.amp.autocast('cuda', enabled=False):
        output_fp32 = F.interpolate(x_fp32, dest.shape[2:], **interp_args)
    if original_dtype != torch.float32:
        output = output_fp32.to(original_dtype)
    else:
        output = output_fp32
    return output

class BasicMotionEncoder(nn.Module):
    def __init__(self, args):
        super(BasicMotionEncoder, self).__init__()
        self.args = args
        cor_planes = args.corr_levels * (2*args.corr_radius + 1) * (8+1)
        self.convc1 = nn.Conv2d(cor_planes, 64, 1, padding=0)
        self.convc2 = nn.Conv2d(64, 64, 3, padding=1)
        self.convd1 = nn.Conv2d(1, 64, 7, padding=3)
        self.convd2 = nn.Conv2d(64, 64, 3, padding=1)
        self.conv = nn.Conv2d(64+64, 128-1, 3, padding=1)

    def forward(self, disp, corr):
        cor = F.relu(self.convc1(corr))
        cor = F.relu(self.convc2(cor))
        disp_ = F.relu(self.convd1(disp))
        disp_ = F.relu(self.convd2(disp_))

        cor_disp = torch.cat([cor, disp_], dim=1)
        out = F.relu(self.conv(cor_disp))
        return torch.cat([out, disp], dim=1)

def pool2x(x):
    return F.avg_pool2d(x, 3, stride=2, padding=1)

def pool4x(x):
    return F.avg_pool2d(x, 5, stride=4, padding=1)

# def interp(x, dest):
#     interp_args = {'mode': 'bilinear', 'align_corners': True}
#     return F.interpolate(x, dest.shape[2:], **interp_args)

class BasicMultiUpdateBlock(nn.Module):
    def __init__(self, args, hidden_dims=[]):
        super().__init__()
        self.args = args
        self.encoder = BasicMotionEncoder(args)
        encoder_output_dim = 128

        self.gru04 = ConvGRU(hidden_dims[2], encoder_output_dim + hidden_dims[1] * (args.n_gru_layers > 1))
        self.gru08 = ConvGRU(hidden_dims[1], hidden_dims[0] * (args.n_gru_layers == 3) + hidden_dims[2])
        self.gru16 = ConvGRU(hidden_dims[0], hidden_dims[1])
        self.disp_head = DispHead(hidden_dims[2], hidden_dim=256, output_dim=1)
        factor = 2**self.args.n_downsample

        self.mask_feat_4 = nn.Sequential(
            nn.Conv2d(hidden_dims[2], 32, 3, padding=1),
            nn.ReLU(inplace=True))

    def forward(self, net, inp, corr=None, disp=None, iter04=True, iter08=True, iter16=True, update=True):

        if iter16:
            net[2] = self.gru16(net[2], *(inp[2]), pool2x(net[1]))
        if iter08:
            if self.args.n_gru_layers > 2:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]), interp(net[2], net[1]))
            else:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]))
        if iter04:
            motion_features = self.encoder(disp, corr)
            if self.args.n_gru_layers > 1:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features, interp(net[1], net[0]))
            else:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features)

        if not update:
            return net

        delta_disp = self.disp_head(net[0])
        mask_feat_4 = 0.2 * self.mask_feat_4(net[0])
        return net, mask_feat_4, delta_disp

class BasicMultiUpdateBlock_mix(nn.Module):
    def __init__(self, args, hidden_dims=[]):
        super().__init__()
        self.args = args
        self.encoder = BasicMotionEncoder_mix(args)
        encoder_output_dim = 128

        self.gru04 = ConvGRU(hidden_dims[2], encoder_output_dim + hidden_dims[1] * (args.n_gru_layers > 1))
        self.gru08 = ConvGRU(hidden_dims[1], hidden_dims[0] * (args.n_gru_layers == 3) + hidden_dims[2])
        self.gru16 = ConvGRU(hidden_dims[0], hidden_dims[1])
        self.disp_head = DispHead(hidden_dims[2], hidden_dim=256, output_dim=2)
        factor = 2**self.args.n_downsample

        self.mask_feat_4 = nn.Sequential(
            nn.Conv2d(hidden_dims[2], 32, 3, padding=1),
            nn.ReLU(inplace=True))

    def forward(self, net, inp, flaw_stereo=None, disp=None, corr=None, flaw_mono=None, disp_mono=None, corr_mono=None, iter04=True, iter08=True, iter16=True, update=True):

        if iter16:
            net[2] = self.gru16(net[2], *(inp[2]), pool2x(net[1]))
        if iter08:
            if self.args.n_gru_layers > 2:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]), interp(net[2], net[1]))
            else:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]))
        if iter04:
            motion_features = self.encoder(disp, corr, flaw_stereo, disp_mono, corr_mono, flaw_mono)
            if self.args.n_gru_layers > 1:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features, interp(net[1], net[0]))
            else:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features)

        if not update:
            return net

        delta_disp_all = self.disp_head(net[0])
        delta_disp = delta_disp_all[:, :1]
        delta_disp_mono = delta_disp_all[:, 1:2]
        mask_feat_4 = self.mask_feat_4(net[0])
        return net, mask_feat_4, delta_disp, delta_disp_mono

class BasicMotionEncoder_mix(nn.Module):
    def __init__(self, args):
        super(BasicMotionEncoder_mix, self).__init__()
        self.args = args
        cor_planes = 96 + args.corr_levels * (2*args.corr_radius + 1) * (8+1)
        self.convc1 = nn.Conv2d(cor_planes, 64, 1, padding=0)
        self.convc2 = nn.Conv2d(64, 64, 3, padding=1)

        self.convc1_mono = nn.Conv2d(cor_planes, 64, 1, padding=0)
        self.convc2_mono = nn.Conv2d(64, 64, 3, padding=1)

        self.convd1 = nn.Conv2d(1, 64, 7, padding=3)
        self.convd2 = nn.Conv2d(64, 64, 3, padding=1)

        self.convd1_mono = nn.Conv2d(1, 64, 7, padding=3)
        self.convd2_mono = nn.Conv2d(64, 64, 3, padding=1)

        self.conv = nn.Conv2d(128, 64-1, 3, padding=1)
        self.conv_mono = nn.Conv2d(128, 64-1, 3, padding=1)


    def forward(self, disp, corr, flaw_stereo, disp_mono, corr_mono, flaw_mono):
        cor = F.relu(self.convc1(torch.cat([corr, flaw_stereo], dim=1)))
        cor = F.relu(self.convc2(cor))
        cor_mono = F.relu(self.convc1_mono(torch.cat([corr_mono, flaw_mono], dim=1)))
        cor_mono = F.relu(self.convc2_mono(cor_mono))

        disp_ = F.relu(self.convd1(disp))
        disp_ = F.relu(self.convd2(disp_))

        disp_mono_ = F.relu(self.convd1_mono(disp_mono))
        disp_mono_ = F.relu(self.convd2_mono(disp_mono_))

        cor_disp = torch.cat([cor, disp_], dim=1)
        cor_disp_mono = torch.cat([cor_mono, disp_mono_], dim=1)

        out = F.relu(self.conv(cor_disp))
        out_mono = F.relu(self.conv_mono(cor_disp_mono))

        return torch.cat([out, disp, out_mono, disp_mono], dim=1)


class BasicMultiUpdateBlock_2(nn.Module):
    def __init__(self, args, hidden_dims=[]):
        super().__init__()
        self.args = args
        self.encoder = BasicMotionEncoder_2(args)
        encoder_output_dim = 128

        self.gru04 = ConvGRU(hidden_dims[2], encoder_output_dim + hidden_dims[1] * (args.n_gru_layers > 1))
        self.gru08 = ConvGRU(hidden_dims[1], hidden_dims[0] * (args.n_gru_layers == 3) + hidden_dims[2])
        self.gru16 = ConvGRU(hidden_dims[0], hidden_dims[1])
        self.disp_head = DispHead(hidden_dims[2], hidden_dim=256, output_dim=1)
        factor = 2**self.args.n_downsample

        self.mask_feat_4 = nn.Sequential(
            nn.Conv2d(hidden_dims[2], 32, 3, padding=1),
            nn.ReLU(inplace=True))

    def forward(self, net, inp, flaw_stereo=None, disp=None, corr=None, confidence=None, flaw_mono=None, disp_mono=None, corr_mono=None, iter04=True, iter08=True, iter16=True, update=True):

        if iter16:
            net[2] = self.gru16(net[2], *(inp[2]), pool2x(net[1]))
        if iter08:
            if self.args.n_gru_layers > 2:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]), interp(net[2], net[1]))
            else:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]))
        if iter04:
            motion_features = self.encoder(disp, corr, flaw_stereo, disp_mono, corr_mono, flaw_mono, confidence)
            if self.args.n_gru_layers > 1:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features, interp(net[1], net[0]))
            else:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features)

        if not update:
            return net

        delta_disp = self.disp_head(net[0])
        mask_feat_4 = self.mask_feat_4(net[0])
        return net, mask_feat_4, delta_disp


class BasicMotionEncoder_2(nn.Module):
    def __init__(self, args):
        super(BasicMotionEncoder_2, self).__init__()
        self.args = args
        cor_planes = 96 + args.corr_levels * (2*args.corr_radius + 1) * (8+1)
        self.convc1 = nn.Conv2d(int(cor_planes + 1), 64, 1, padding=0)
        self.convc2 = nn.Conv2d(64, 64, 3, padding=1)

        self.convc1_mono = nn.Conv2d(cor_planes, 64, 1, padding=0)
        self.convc2_mono = nn.Conv2d(64, 64, 3, padding=1)

        self.convd1 = nn.Conv2d(1, 64, 7, padding=3)
        self.convd2 = nn.Conv2d(64, 64, 3, padding=1)

        self.convd1_mono = nn.Conv2d(1, 64, 7, padding=3)
        self.convd2_mono = nn.Conv2d(64, 64, 3, padding=1)

        self.conv = nn.Conv2d(129, 64-1, 3, padding=1)
        self.conv_mono = nn.Conv2d(128, 64-1, 3, padding=1)


    def forward(self, disp, corr, flaw_stereo, disp_mono, corr_mono, flaw_mono, confidence):
        cor = F.relu(self.convc1(torch.cat([corr, flaw_stereo, confidence], dim=1)))
        cor = F.relu(self.convc2(cor))
        cor_mono = F.relu(self.convc1_mono(torch.cat([corr_mono, flaw_mono], dim=1)))
        cor_mono = F.relu(self.convc2_mono(cor_mono))

        disp_ = F.relu(self.convd1(disp))
        disp_ = F.relu(self.convd2(disp_))

        disp_mono_ = F.relu(self.convd1_mono(disp_mono))
        disp_mono_ = F.relu(self.convd2_mono(disp_mono_))

        cor_disp = torch.cat([cor, disp_, confidence], dim=1)
        cor_disp_mono = torch.cat([cor_mono, disp_mono_], dim=1)

        out = F.relu(self.conv(cor_disp))
        out_mono = F.relu(self.conv_mono(cor_disp_mono))

        return torch.cat([out, disp, out_mono, disp_mono], dim=1)
    

class BasicMultiUpdateBlock_mono(nn.Module):
    def __init__(self, args, hidden_dims=[]):
        super().__init__()
        self.args = args
        self.encoder = BasicMotionEncoder_mono(args)
        encoder_output_dim = 128

        self.gru04 = ConvGRU(hidden_dims[2], encoder_output_dim + hidden_dims[1] * (args.n_gru_layers > 1))
        self.gru08 = ConvGRU(hidden_dims[1], hidden_dims[0] * (args.n_gru_layers == 3) + hidden_dims[2])
        self.gru16 = ConvGRU(hidden_dims[0], hidden_dims[1])
        self.disp_head = DispHead(hidden_dims[2], hidden_dim=256, output_dim=1)
        factor = 2**self.args.n_downsample

        self.mask_feat_4 = nn.Sequential(
            nn.Conv2d(hidden_dims[2], 32, 3, padding=1),
            nn.ReLU(inplace=True))

    def forward(self, net, inp, corr=None, disp=None, iter04=True, iter08=True, iter16=True, update=True):

        if iter16:
            net[2] = self.gru16(net[2], *(inp[2]), pool2x(net[1]))
        if iter08:
            if self.args.n_gru_layers > 2:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]), interp(net[2], net[1]))
            else:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]))
        if iter04:
            motion_features = self.encoder(disp, corr)
            if self.args.n_gru_layers > 1:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features, interp(net[1], net[0]))
            else:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features)

        if not update:
            return net

        delta_disp = self.disp_head(net[0])
        mask_feat_4 = self.mask_feat_4(net[0])
        return net, mask_feat_4, delta_disp


class BasicMotionEncoder_mono(nn.Module):
    def __init__(self, args):
        super(BasicMotionEncoder_mono, self).__init__()
        self.args = args
        cor_planes = args.corr_levels * (2*args.corr_radius + 1) * (8+1)
        self.convc1 = nn.Conv2d(cor_planes, 64, 1, padding=0)
        self.convc2 = nn.Conv2d(64, 64, 3, padding=1)
        self.convd1 = nn.Conv2d(1, 64, 7, padding=3)
        self.convd2 = nn.Conv2d(64, 64, 3, padding=1)
        self.conv = nn.Conv2d(64+64, 128-1, 3, padding=1)

    def forward(self, disp, corr):
        cor = F.relu(self.convc1(corr))
        cor = F.relu(self.convc2(cor))
        disp_ = F.relu(self.convd1(disp))
        disp_ = F.relu(self.convd2(disp_))

        cor_disp = torch.cat([cor, disp_], dim=1)
        out = F.relu(self.conv(cor_disp))
        return torch.cat([out, disp], dim=1)


class BasicMultiUpdateBlock_mix_conf(nn.Module):
    def __init__(self, args, hidden_dims=[]):
        super().__init__()
        self.args = args
        self.encoder = BasicMotionEncoder_mix_conf(args)
        encoder_output_dim = 128

        self.gru04 = ConvGRU(hidden_dims[2], encoder_output_dim + hidden_dims[1] * (args.n_gru_layers > 1))
        self.gru08 = ConvGRU(hidden_dims[1], hidden_dims[0] * (args.n_gru_layers == 3) + hidden_dims[2])
        self.gru16 = ConvGRU(hidden_dims[0], hidden_dims[1])
        self.disp_head = DispHead(hidden_dims[2], hidden_dim=256, output_dim=2)
        factor = 2**self.args.n_downsample

        self.mask_feat_4 = nn.Sequential(
            nn.Conv2d(hidden_dims[2], 32, 3, padding=1),
            nn.ReLU(inplace=True))

    def forward(self, net, inp, flaw_stereo=None, disp=None, corr=None, flaw_mono=None, disp_mono=None, corr_mono=None, conf_stereo=None, conf_mono=None, iter04=True, iter08=True, iter16=True, update=True):

        if iter16:
            net[2] = self.gru16(net[2], *(inp[2]), pool2x(net[1]))
        if iter08:
            if self.args.n_gru_layers > 2:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]), interp(net[2], net[1]))
            else:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]))
        if iter04:
            motion_features = self.encoder(disp, corr, flaw_stereo, disp_mono, corr_mono, flaw_mono, conf_stereo, conf_mono)
            if self.args.n_gru_layers > 1:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features, interp(net[1], net[0]))
            else:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features)

        if not update:
            return net

        delta_disp_all = self.disp_head(net[0])
        delta_disp = delta_disp_all[:, :1]
        delta_disp_mono = delta_disp_all[:, 1:2]
        mask_feat_4 = self.mask_feat_4(net[0])
        return net, mask_feat_4, delta_disp, delta_disp_mono
    

class BasicMotionEncoder_mix_conf(nn.Module):
    def __init__(self, args):
        super(BasicMotionEncoder_mix_conf, self).__init__()
        self.args = args
        cor_planes = 96 + args.corr_levels * (2*args.corr_radius + 1) * (8+1)

        self.conv_conf1 = nn.Conv2d(1, 64, 7, padding=3)
        self.conv_conf2 = nn.Conv2d(64, 64, 3, padding=1)

        self.conv_conf1_mono = nn.Conv2d(1, 64, 7, padding=3)
        self.conv_conf2_mono = nn.Conv2d(64, 64, 3, padding=1)

        self.convc1 = nn.Conv2d(int(cor_planes + 64), 64, 1, padding=0)
        self.convc2 = nn.Conv2d(64, 64, 3, padding=1)

        self.convc1_mono = nn.Conv2d(int(cor_planes + 64), 64, 1, padding=0)
        self.convc2_mono = nn.Conv2d(64, 64, 3, padding=1)

        self.convd1 = nn.Conv2d(1, 64, 7, padding=3)
        self.convd2 = nn.Conv2d(64, 64, 3, padding=1)

        self.convd1_mono = nn.Conv2d(1, 64, 7, padding=3)
        self.convd2_mono = nn.Conv2d(64, 64, 3, padding=1)

        self.conv = nn.Conv2d(128, 64-2, 3, padding=1)
        self.conv_mono = nn.Conv2d(128, 64-2, 3, padding=1)


    def forward(self, disp, corr, flaw_stereo, disp_mono, corr_mono, flaw_mono, conf_stereo, conf_mono):

        conf_stereo_ = F.relu(self.conv_conf1(conf_stereo))
        conf_stereo_ = F.relu(self.conv_conf2(conf_stereo_))

        conf_mono_ = F.relu(self.conv_conf1_mono(conf_mono))
        conf_mono_ = F.relu(self.conv_conf2_mono(conf_mono_))

        cor = F.relu(self.convc1(torch.cat([corr, flaw_stereo, conf_stereo_], dim=1)))
        cor = F.relu(self.convc2(cor))
        cor_mono = F.relu(self.convc1_mono(torch.cat([corr_mono, flaw_mono, conf_mono_], dim=1)))
        cor_mono = F.relu(self.convc2_mono(cor_mono))

        disp_ = F.relu(self.convd1(disp))
        disp_ = F.relu(self.convd2(disp_))

        disp_mono_ = F.relu(self.convd1_mono(disp_mono))
        disp_mono_ = F.relu(self.convd2_mono(disp_mono_))

        cor_disp = torch.cat([cor, disp_], dim=1)
        cor_disp_mono = torch.cat([cor_mono, disp_mono_], dim=1)

        out = F.relu(self.conv(cor_disp))
        out_mono = F.relu(self.conv_mono(cor_disp_mono))

        return torch.cat([out, disp, conf_stereo, out_mono, disp_mono, conf_mono], dim=1)


class BasicMultiUpdateBlock_mix2(nn.Module):
    def __init__(self, args, hidden_dims=[]):
        super().__init__()
        self.args = args
        self.encoder = BasicMotionEncoder_mix2(args)
        encoder_output_dim = 128

        self.gru04 = ConvGRU(hidden_dims[2], encoder_output_dim + hidden_dims[1] * (args.n_gru_layers > 1))
        self.gru08 = ConvGRU(hidden_dims[1], hidden_dims[0] * (args.n_gru_layers == 3) + hidden_dims[2])
        self.gru16 = ConvGRU(hidden_dims[0], hidden_dims[1])
        self.disp_head = DispHead(hidden_dims[2], hidden_dim=256, output_dim=1)
        factor = 2**self.args.n_downsample

        self.mask_feat_4 = nn.Sequential(
            nn.Conv2d(hidden_dims[2], 32, 3, padding=1),
            nn.ReLU(inplace=True))

    def forward(self, net, inp, flaw_stereo=None, disp=None, corr=None, flaw_mono=None, disp_mono=None, corr_mono=None, iter04=True, iter08=True, iter16=True, update=True):

        if iter16:
            net[2] = self.gru16(net[2], *(inp[2]), pool2x(net[1]))
        if iter08:
            if self.args.n_gru_layers > 2:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]), interp(net[2], net[1]))
            else:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]))
        if iter04:
            motion_features = self.encoder(disp, corr, flaw_stereo, disp_mono, corr_mono, flaw_mono)
            if self.args.n_gru_layers > 1:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features, interp(net[1], net[0]))
            else:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features)

        if not update:
            return net
        delta_disp = self.disp_head(net[0])
        mask_feat_4 = self.mask_feat_4(net[0])
        return net, mask_feat_4, delta_disp

class BasicMotionEncoder_mix2(nn.Module):
    def __init__(self, args):
        super(BasicMotionEncoder_mix2, self).__init__()
        self.args = args
        cor_planes = 96 + args.corr_levels * (2*args.corr_radius + 1) * (8+1)

        self.convc1 = nn.Conv2d(cor_planes, 64, 1, padding=0)
        self.convc2 = nn.Conv2d(64, 64, 3, padding=1)

        self.convc1_mono = nn.Conv2d(cor_planes, 64, 1, padding=0)
        self.convc2_mono = nn.Conv2d(64, 64, 3, padding=1)

        self.convd1 = nn.Conv2d(1, 64, 7, padding=3)
        self.convd2 = nn.Conv2d(64, 64, 3, padding=1)

        self.convd1_mono = nn.Conv2d(1, 64, 7, padding=3)
        self.convd2_mono = nn.Conv2d(64, 64, 3, padding=1)

        self.conv = nn.Conv2d(128, 64-1, 3, padding=1)
        self.conv_mono = nn.Conv2d(128, 64-1, 3, padding=1)


    def forward(self, disp, corr, flaw_stereo, disp_mono, corr_mono, flaw_mono):
        cor = F.relu(self.convc1(torch.cat([corr, flaw_stereo], dim=1)))
        cor = F.relu(self.convc2(cor))
        cor_mono = F.relu(self.convc1_mono(torch.cat([corr_mono, flaw_mono], dim=1)))
        cor_mono = F.relu(self.convc2_mono(cor_mono))

        disp_ = F.relu(self.convd1(disp))
        disp_ = F.relu(self.convd2(disp_))

        disp_mono_ = F.relu(self.convd1_mono(disp_mono))
        disp_mono_ = F.relu(self.convd2_mono(disp_mono_))

        cor_disp = torch.cat([cor, disp_], dim=1)
        cor_disp_mono = torch.cat([cor_mono, disp_mono_], dim=1)

        out = F.relu(self.conv(cor_disp))
        out_mono = F.relu(self.conv_mono(cor_disp_mono))

        return torch.cat([out, disp, out_mono, disp_mono], dim=1)



class MotionEncoder_Detail(nn.Module):
    """
    简化的单分支运动编码器
    
    设计原理：
    1. 单一编码分支，处理geo_feat + flaw + attention_features
    2. 直接融合所有特征，输出统一的运动表示
    3. 轻量级设计，减少参数量和计算开销
    """
    def __init__(self, args):
        super().__init__()
        self.args = args
        # 单层级采样的geo_feat维度计算
        # 单层级：1 * (2*radius + 1) * (8+1) = 1 * 9 * 9 = 81
        single_level_corr = 2 * (2*args.corr_radius + 1) 
        # 输入特征：geo_feat(81) + flaw(96) + occ_mask(1) = 178
        cor_planes_single = single_level_corr + 81
    
        # 相关性特征编码（单分支）
        self.convc1 = nn.Conv2d(single_level_corr, 128, 7, padding=3)  # 输入: 178通道
        self.convc2 = nn.Conv2d(128, 128, 7, padding=3)
        self.convc3 = nn.Conv2d(128, 64, 7, padding=3)
        
        # 视差编码（单分支）
        self.convd1 = nn.Conv2d(1, 64, 7, padding=3)
        self.convd2 = nn.Conv2d(64, 64, 7, padding=3)

        # 最终融合（单分支输出）
        self.conv = nn.Conv2d(128, 128-1, 3, padding=1)  # 输出127维特征
    
    def forward(self, disp, corr, attn):
        """
        简化的单分支前向传播
        
        Args:
            disp: [B, 1, H, W] 立体匹配的当前视差图  
            corr: [B, 81, H, W] 单层级几何相关性特征
            flaw: [B, 96, H, W] warped_right - features_left 的差异特征
            
        Returns:
            motion_features: [B, 128, H, W] 统一的运动特征
        """
       
        # combined_features = torch.cat([corr, attn], dim=1)  # [B, 178, H, W]
        # 相关性特征编码
        # cor = F.gelu(self.convc1(combined_features))  # [B, 64, H, W]
        cor = F.gelu(self.convc1(corr))
        cor = F.gelu(self.convc2(cor))                # [B, 64, H, W]
        cor = F.gelu(self.convc3(cor))                # [B, 64, H, W]
        
        # 视差编码
        disp_ = F.relu(self.convd1(disp))             # [B, 64, H, W]
        disp_ = F.relu(self.convd2(disp_))            # [B, 64, H, W]
        
        # 特征融合
        cor_disp = torch.cat([cor, disp_], dim=1)     # [B, 128, H, W]
        
        # 最终输出
        out = F.gelu(self.conv(cor_disp))             # [B, 127, H, W]
        
        # 返回完整的运动特征（包含视差）
        return torch.cat([out, disp], dim=1)          # [B, 128, H, W]


class MotionEncoder_Occ(nn.Module):
    
    def __init__(self, args):
        super().__init__()
        self.args = args
        # 单层级采样的geo_feat维度计算
        # 单层级：1 * (2*radius + 1) * (8+1) = 1 * 9 * 9 = 81
        single_level_corr = 1 * (2*args.corr_radius + 1) * (8+1)
        # 输入特征：geo_feat(81) + flaw(96) + occ_mask(1) = 178
    
        # 相关性特征编码（单分支）
        self.convc1 = nn.Conv2d(83, 256, 1, padding=0)  # 输入: 178通道
        self.convc2 = nn.Conv2d(256, 64, 3, padding=1)
        
        # 视差编码（单分支）
        self.convd1 = nn.Conv2d(1, 64, 7, padding=3)
        self.convd2 = nn.Conv2d(64, 64, 3, padding=1)

        # 最终融合（单分支输出）
        self.conv = nn.Conv2d(128, 128-1, 3, padding=1)  # 输出127维特征
    
    def forward(self, disp, occ_mask, attn):
       
        combined_features = torch.cat([disp, occ_mask, attn], dim=1)  # [B, 178, H, W]
       
        cor = F.relu(self.convc1(combined_features))  # [B, 64, H, W]
        cor = F.relu(self.convc2(cor))                # [B, 64, H, W]
        
        # 视差编码
        disp_ = F.relu(self.convd1(disp))             # [B, 64, H, W]
        disp_ = F.relu(self.convd2(disp_))            # [B, 64, H, W]
        
        # 特征融合
        cor_disp = torch.cat([cor, disp_], dim=1)     # [B, 128, H, W]
        
        # 最终输出
        out = F.relu(self.conv(cor_disp))             # [B, 127, H, W]
        
        # 返回完整的运动特征（包含视差）
        return torch.cat([out, disp], dim=1)          # [B, 128, H, W]


class UpdateBlock_Detail(nn.Module):
    def __init__(self, args, hidden_dims):
        super().__init__()
        self.args = args
        self.encoder = MotionEncoder_Detail(args)  # 使用单层级编码器
        
        # GRU结构（与多层级版本相同）
        encoder_output_dim = 128
        self.gru04 = ConvGRU(hidden_dims[2], encoder_output_dim + hidden_dims[1] * (args.n_gru_layers > 1))
        self.gru08 = ConvGRU(hidden_dims[1], hidden_dims[0] * (args.n_gru_layers == 3) + hidden_dims[2])
        self.gru16 = ConvGRU(hidden_dims[0], hidden_dims[1])
        
        # 输出统一的视差增量
        self.disp_head = DispHead(hidden_dims[2], hidden_dim=256, output_dim=1)
        # self.occ_mask_head = DispHead(hidden_dims[2], hidden_dim=256, output_dim=1)          
        
        self.mask_feat_4 = nn.Sequential(
            nn.Conv2d(hidden_dims[2], 32, 3, padding=1),
            nn.ReLU(inplace=True)
        )
        
        # 预计算条件
        self.has_iter16 = args.n_gru_layers == 3
        self.has_iter08 = args.n_gru_layers >= 2
        self.has_multiple_layers = args.n_gru_layers > 1
        self.has_three_layers = args.n_gru_layers > 2
    
    def forward(self, net, inp, disp, corr,attn_map,
                iter04=True, iter08=True, iter16=True, update=True):
        """
        针对单层级geo_feat优化的处理
        """
        # GRU更新（与多层级版本相同）
        if iter16 and self.has_iter16:
            net[2] = self.gru16(net[2], *(inp[2]), pool2x(net[1]))
        
        if iter08 and self.has_iter08:
            if self.has_three_layers:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]), interp(net[2], net[1]))
            else:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]))
        
        if iter04:
            motion_features = self.encoder(disp, corr, attn_map)
            
            if self.has_multiple_layers:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features, interp(net[1], net[0]))
            else:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features)
        
        if not update:
            return net
        
        # 输出统一的视差增量
        delta_disp = self.disp_head(net[0])
        mask_feat_4 = 0.2*self.mask_feat_4(net[0])
        # occ_mask = F.sigmoid(self.occ_mask_head(net[0]))
        
        return net, delta_disp, mask_feat_4



class UpdateBlock_Occ(nn.Module):
    """
    针对单层级采样优化的更新块
    专门处理后期精细化阶段的更新，使用更小维度的geo_feat
    """
    def __init__(self, args, hidden_dims):
        super().__init__()
        self.args = args
        self.encoder = MotionEncoder_Occ(args)  # 使用单层级编码器
        
        # GRU结构（与多层级版本相同）
        encoder_output_dim = 128
        self.gru04 = ConvGRU(hidden_dims[2], encoder_output_dim + hidden_dims[1] * (args.n_gru_layers > 1))
        self.gru08 = ConvGRU(hidden_dims[1], hidden_dims[0] * (args.n_gru_layers == 3) + hidden_dims[2])
        self.gru16 = ConvGRU(hidden_dims[0], hidden_dims[1])
        
        # 输出统一的视差增量
        self.disp_head = DispHead(hidden_dims[2], hidden_dim=256, output_dim=81)
        self.occ_head = DispHead(hidden_dims[2], hidden_dim=256, output_dim=1)
        
        # 预计算条件
        self.has_iter16 = args.n_gru_layers == 3
        self.has_iter08 = args.n_gru_layers >= 2
        self.has_multiple_layers = args.n_gru_layers > 1
        self.has_three_layers = args.n_gru_layers > 2
    
    def forward(self, net, inp, disp, attn, occ_mask,
                iter04=True, iter08=True, iter16=True, update=True):
        """
        针对单层级geo_feat优化的处理
        """
        # GRU更新（与多层级版本相同）
        if iter16 and self.has_iter16:
            net[2] = self.gru16(net[2], *(inp[2]), pool2x(net[1]))
        
        if iter08 and self.has_iter08:
            if self.has_three_layers:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]), interp(net[2], net[1]))
            else:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]))
        
        if iter04:
            motion_features = self.encoder(disp, attn, occ_mask)
            
            if self.has_multiple_layers:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features, interp(net[1], net[0]))
            else:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features)
        
        if not update:
            return net
        
        # 输出统一的视差增量
        adaptive_wight = F.softmax(self.disp_head(net[0]), dim=1)
        delta_occ = F.sigmoid(self.occ_head(net[0]))
        B, _, H, W = disp.shape
        disp_expand = torch.nn.functional.unfold(disp, kernel_size=9, stride=1, padding=4).view(B, 81, H, W)
        update_disp = torch.mean(adaptive_wight * disp_expand, dim=1, keepdim=True)
        
        return net, update_disp, delta_occ


    


# Correlation-only encoders for geo_encoding_volume-free design

class BasicMotionEncoder_CorrelationOnly(nn.Module):
    """
    仅使用特征相关性的运动编码器，不依赖geo_encoding_volume
    
    维度计算：
    - 原版本：args.corr_levels * (2*args.corr_radius + 1) * (8+1) = levels * 9 * 9 = levels * 81
    - 新版本：args.corr_levels * (2*args.corr_radius + 1) * 1 = levels * 9 * 1 = levels * 9
    """
    def __init__(self, args):
        super(BasicMotionEncoder_CorrelationOnly, self).__init__()
        self.args = args
        # 仅使用特征相关性，移除geo_volume的8个通道
        cor_planes = args.corr_levels * (2*args.corr_radius + 1) * 1  # 大幅减少维度
        self.convc1 = nn.Conv2d(cor_planes, 64, 1, padding=0)
        self.convc2 = nn.Conv2d(64, 64, 3, padding=1)
        self.convd1 = nn.Conv2d(1, 64, 7, padding=3)
        self.convd2 = nn.Conv2d(64, 64, 3, padding=1)
        self.conv = nn.Conv2d(64+64, 128-1, 3, padding=1)

    def forward(self, disp, corr):
        cor = F.relu(self.convc1(corr))
        cor = F.relu(self.convc2(cor))
        disp_ = F.relu(self.convd1(disp))
        disp_ = F.relu(self.convd2(disp_))

        cor_disp = torch.cat([cor, disp_], dim=1)
        out = F.relu(self.conv(cor_disp))
        return torch.cat([out, disp], dim=1)


class BasicMultiUpdateBlock_CorrelationOnly(nn.Module):
    """
    使用仅相关性编码器的更新块
    """
    def __init__(self, args, hidden_dims=[]):
        super().__init__()
        self.args = args
        self.encoder = BasicMotionEncoder_CorrelationOnly(args)
        encoder_output_dim = 128

        self.gru04 = ConvGRU(hidden_dims[2], encoder_output_dim + hidden_dims[1] * (args.n_gru_layers > 1))
        self.gru08 = ConvGRU(hidden_dims[1], hidden_dims[0] * (args.n_gru_layers == 3) + hidden_dims[2])
        self.gru16 = ConvGRU(hidden_dims[0], hidden_dims[1])
        self.disp_head = DispHead(hidden_dims[2], hidden_dim=256, output_dim=1)
        factor = 2**self.args.n_downsample

        self.mask_feat_4 = nn.Sequential(
            nn.Conv2d(hidden_dims[2], 32, 3, padding=1),
            nn.ReLU(inplace=True))

    def forward(self, net, inp, corr=None, disp=None, iter04=True, iter08=True, iter16=True, update=True):

        if iter16:
            net[2] = self.gru16(net[2], *(inp[2]), pool2x(net[1]))
        if iter08:
            if self.args.n_gru_layers > 2:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]), interp(net[2], net[1]))
            else:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]))
        if iter04:
            motion_features = self.encoder(disp, corr)
            if self.args.n_gru_layers > 1:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features, interp(net[1], net[0]))
            else:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features)

        if not update:
            return net

        delta_disp = self.disp_head(net[0])
        mask_feat_4 = self.mask_feat_4(net[0])
        return net, mask_feat_4, delta_disp


class MotionEncoder_Detail_CorrelationOnly(nn.Module):
    """
    仅使用相关性的精细化运动编码器，不依赖geo_encoding_volume
    
    维度计算：
    - 原版本：1 * (2*radius + 1) * (8+1) + 96 = 1 * 9 * 9 + 96 = 81 + 96 = 177
    - 新版本：1 * (2*radius + 1) * 1 + 96 = 1 * 9 * 1 + 96 = 9 + 96 = 105
    """
    def __init__(self, args):
        super().__init__()
        self.args = args
        # 单层级采样的仅相关性geo_feat维度计算
        # 单层级：1 * (2*radius + 1) * 1 = 1 * 9 * 1 = 9
        single_level_corr = 1 * (2*args.corr_radius + 1) * 1  # 大幅减少维度
        # 加上flaw特征：9 + 96 = 105
        cor_planes_single = single_level_corr + 96
        
        # 立体匹配分支（处理单层级相关性 + flaw）
        self.convc1 = nn.Conv2d(cor_planes_single, 64, 1, padding=0)
        self.convc2 = nn.Conv2d(64, 64, 3, padding=1)
        
        # flaw分支（同样处理单层级相关性 + flaw）
        self.convc1_flaw = nn.Conv2d(cor_planes_single, 64, 1, padding=0)
        self.convc2_flaw = nn.Conv2d(64, 64, 3, padding=1)
        
        # 视差编码（立体+单目双分支）
        self.convd1 = nn.Conv2d(1, 64, 7, padding=3)
        self.convd2 = nn.Conv2d(64, 64, 3, padding=1)
        
        self.convd1_flaw = nn.Conv2d(1, 64, 7, padding=3)  # 为flaw对应的视差编码
        self.convd2_flaw = nn.Conv2d(64, 64, 3, padding=1)
    
        
        # 融合层（输出与原版本兼容的128维特征）
        self.conv_fusion = nn.Conv2d(128, 128-1, 3, padding=1)

    def forward(self, disp, corr, flaw):
        """
        处理仅相关性的geo_feat + flaw特征
        
        Args:
            disp: 当前视差图 [B, 1, H, W]
            corr: 仅相关性的geo_feat [B, 9, H, W] (单层级)
            flaw: warping差异特征 [B, 96, H, W]
        
        Returns:
            运动特征 [B, 128, H, W]
        """
        
        # 立体匹配分支：相关性+flaw特征
        corr_flaw = torch.cat([corr, flaw], dim=1)  # [B, 105, H, W]
        
        # 立体分支处理
        cor_stereo = F.relu(self.convc1(corr_flaw))
        cor_stereo = F.relu(self.convc2(cor_stereo))
        
        # flaw分支处理（使用注意力机制）
        cor_flaw_weighted = F.relu(self.convc1_flaw(corr_flaw))
        cor_flaw_weighted = F.relu(self.convc2_flaw(cor_flaw_weighted))
        
        # 视差编码
        disp_stereo = F.relu(self.convd1(disp))
        disp_stereo = F.relu(self.convd2(disp_stereo))
        
        disp_flaw = F.relu(self.convd1_flaw(disp))
        disp_flaw = F.relu(self.convd2_flaw(disp_flaw))
        
        # 特征融合
        fusion_features = torch.cat([cor_stereo, disp_stereo], dim=1)  # [B, 128, H, W]
        
        motion_features = F.relu(self.conv_fusion(fusion_features))  # [B, 127, H, W]
        
        return torch.cat([motion_features, disp], dim=1)  # [B, 128, H, W]


class UpdateBlock_Detail_CorrelationOnly(nn.Module):
    """
    仅使用相关性的精细化更新块
    """
    def __init__(self, args, hidden_dims):
        super().__init__()
        self.args = args
        self.encoder = MotionEncoder_Detail_CorrelationOnly(args)  # 使用仅相关性编码器
        
        # GRU结构（与多层级版本相同）
        encoder_output_dim = 128
        self.gru04 = ConvGRU(hidden_dims[2], encoder_output_dim + hidden_dims[1] * (args.n_gru_layers > 1))
        self.gru08 = ConvGRU(hidden_dims[1], hidden_dims[0] * (args.n_gru_layers == 3) + hidden_dims[2])
        self.gru16 = ConvGRU(hidden_dims[0], hidden_dims[1])
        
        # 输出统一的视差增量
        self.disp_head = DispHead(hidden_dims[2], hidden_dim=256, output_dim=1)
        
        self.mask_feat_4 = nn.Sequential(
            nn.Conv2d(hidden_dims[2], 32, 3, padding=1),
            nn.ReLU(inplace=True)
        )
        
        # 预计算条件
        self.has_iter16 = args.n_gru_layers == 3
        self.has_iter08 = args.n_gru_layers >= 2
        self.has_multiple_layers = args.n_gru_layers > 1
        self.has_three_layers = args.n_gru_layers > 2
    
    def forward(self, net, inp, disp, corr, flaw,
                iter04=True, iter08=True, iter16=True, update=True):
        """
        针对仅相关性geo_feat优化的处理
        """
        # GRU更新（与多层级版本相同）
        if iter16 and self.has_iter16:
            net[2] = self.gru16(net[2], *(inp[2]), pool2x(net[1]))
        
        if iter08 and self.has_iter08:
            if self.has_three_layers:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]), interp(net[2], net[1]))
            else:
                net[1] = self.gru08(net[1], *(inp[1]), pool2x(net[0]))
        
        if iter04:
            motion_features = self.encoder(disp, corr, flaw)
            
            if self.has_multiple_layers:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features, interp(net[1], net[0]))
            else:
                net[0] = self.gru04(net[0], *(inp[0]), motion_features)
        
        if not update:
            return net
        
        # 输出统一的视差增量
        delta_disp = self.disp_head(net[0])
        mask_feat_4 = self.mask_feat_4(net[0])
        
        return net, mask_feat_4, delta_disp
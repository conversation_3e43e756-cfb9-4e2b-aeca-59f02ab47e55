import torch
import torch.nn as nn
import torch.nn.functional as F
import sys

# Core components
from core.update import ConvGRU, BasicMultiUpdateBlock, UpdateBlock_Detail, UpdateBlock_Occ
from core.geometry import OptimizedCombinedGeoEncoding

from core.submodule import *
from core.warp import disp_warp
from core.config import ModelConfig
from core.utils.utils import pool2x, interp
from core.attn import FeatureEnhancer, MultiScaleFeatureEnhancer
from core.upsample import DisparityUpsampler
import math


sys.path.append('./Depth-Anything-V2-list3')
from depth_anything_v2.dpt import DepthAnythingV2, DepthAnythingV2_decoder

# 🚫 MonocularDisparityDecoder - 已禁用单目视差解码器
class MonocularDisparityDecoder(nn.Module):
    """
    单目视差解码器
    从多尺度特征预测单目视差和不确定性
    
    设计原理：
    1. 多尺度特征融合：结合不同分辨率的语义信息
    2. 不确定性估计：同时预测视差和置信度sigma_map
    3. 轻量级设计：减少计算开销，保持实时性能
    """
    
    def __init__(self, feature_dims=[96, 96, 96, 96], hidden_dim=128):
        super().__init__()
        
        # 多尺度特征投影
        self.feature_projectors = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(dim, hidden_dim//4, 3, 1, 1),
                nn.ReLU(inplace=True),
                nn.Conv2d(hidden_dim//4, hidden_dim//4, 3, 1, 1),
                nn.ReLU(inplace=True)
            ) for dim in feature_dims
        ])
        
        # 特征融合网络
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(hidden_dim, hidden_dim, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_dim, hidden_dim//2, 3, 1, 1),
            nn.ReLU(inplace=True)
        )
        
        # 视差预测头
        self.disp_head = nn.Sequential(
            nn.Conv2d(hidden_dim//2, hidden_dim//4, 3, 1, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_dim//4, 1, 3, 1, 1),
            nn.ReLU(inplace=True)  # 确保视差为正值
        )
        
    def forward(self, features_list):
        """
        Args:
            features_list: 多尺度特征列表 [feat_4x, feat_8x, feat_16x, feat_32x]
            
        Returns:
            disp_mono: 单目视差预测 [B, 1, H, W]
            sigma_map: 不确定性图 [B, 1, H, W]
        """
        # 获取目标分辨率（使用最高分辨率特征）
        target_size = features_list[0].shape[-2:]
        
        # 投影所有特征到相同维度并上采样到目标分辨率
        projected_features = []
        for i, (feat, projector) in enumerate(zip(features_list, self.feature_projectors)):
            proj_feat = projector(feat)
            if proj_feat.shape[-2:] != target_size:
                proj_feat = F.interpolate(proj_feat, size=target_size, mode='bilinear', align_corners=True)
            projected_features.append(proj_feat)
        
        # 拼接所有投影特征
        fused_features = torch.cat(projected_features, dim=1)  # [B, hidden_dim, H, W]
        
        # 特征融合
        fusion_output = self.feature_fusion(fused_features)  # [B, hidden_dim//2, H, W]
        
        # 预测视差和不确定性
        disp_mono = self.disp_head(fusion_output)  # [B, 1, H, W]
        
        return disp_mono


class Monster(nn.Module):
    """
    Monster+ unified stereo matching network.
    
    Combines stereo geometric constraints and monocular semantic information
    for unified depth estimation.
    """

    def __init__(self, args):
        super().__init__()
        self.args = args
        
        # Initialize model configurations
        self.intermediate_layer_idx = ModelConfig.INTERMEDIATE_LAYER_IDX
        self.mono_model_configs = ModelConfig.MONO_MODEL_CONFIGS
        
        # Get dimension configuration
        dim_list_ = self.mono_model_configs[self.args.encoder]['features']
        self.dim_list = [dim_list_]
    
        # Initialize context networks and feature transfer modules
        self.context_zqr_convs = nn.ModuleList([
            nn.Conv2d(self.args.hidden_dims[i], self.args.hidden_dims[i]*3, 3, padding=3//2) 
            for i in range(self.args.n_gru_layers)
        ])
        
        self.feat_transfer = FeatureTransfer(self.dim_list)
        self.feat_transfer_cnet = ContextFeatureTransfer(self.dim_list, output_dim=self.args.hidden_dims[0])

        # Initialize CNN feature extraction layers
        self.stem_2 = nn.Sequential(
            BasicConv_IN(3, 32, kernel_size=3, stride=2, padding=1),
            nn.Conv2d(32, 32, 3, 1, 1, bias=False),
            nn.InstanceNorm2d(32), nn.ReLU())

        self.stem_4 = nn.Sequential(
            BasicConv_IN(32, 48, kernel_size=3, stride=2, padding=1),
            nn.Conv2d(48, 48, 3, 1, 1, bias=False),
            nn.InstanceNorm2d(48), nn.ReLU())

        self.stem_8 = nn.Sequential(
            BasicConv_IN(48, 96, kernel_size=3, stride=2, padding=1),
            nn.Conv2d(96, 96, 3, 1, 1, bias=False),
            nn.InstanceNorm2d(96), nn.ReLU())
        
        self.stem_16 = nn.Sequential(
            BasicConv_IN(96, 192, kernel_size=3, stride=2, padding=1),
            nn.Conv2d(192, 192, 3, 1, 1, bias=False),
            nn.InstanceNorm2d(192), nn.ReLU())
    
        # Initialize stereo matching components
        # Upsampling components
        self.spx = nn.Sequential(nn.ConvTranspose2d(2*32, 9, kernel_size=4, stride=2, padding=1))
        self.spx_2 = Conv2x_IN(24, 32, True)
        self.spx_4 = nn.Sequential(
            BasicConv_IN(96, 24, kernel_size=3, stride=1, padding=1),
            nn.Conv2d(24, 24, 3, 1, 1, bias=False),
            nn.InstanceNorm2d(24), nn.ReLU()
        )
        self.spx_2_gru = Conv2x(32, 32, True)
        self.spx_gru = nn.Sequential(nn.ConvTranspose2d(2*32, 9, kernel_size=4, stride=2, padding=1))
        # self.context_conv = BasicConv_IN(256, 32, kernel_size=3, padding=1, stride=1)

        # Stereo matching components
        self.conv = BasicConv_IN(96, 96, kernel_size=3, padding=1, stride=1)
        self.desc = nn.Conv2d(96, 96, kernel_size=1, padding=0, stride=1)

        # Cost volume processing
        self.corr_stem = BasicConv(8, 8, is_3d=True, kernel_size=3, stride=1, padding=1)
        self.corr_feature_att = FeatureAtt(8, 96)
        self.cost_agg = HourglassNetwork(8)
        self.classifier_coarse = nn.Conv3d(8, 1, 3, 1, 1, bias=False)
        self.classifier_fine = nn.Conv3d(8, 1, 3, 1, 1, bias=False)

      
        # Initialize monocular depth estimation components
        config = self.mono_model_configs[self.args.encoder]
        
        depth_anything = DepthAnythingV2(**config)
        depth_anything_decoder = DepthAnythingV2_decoder(**config)
        
        self.mono_encoder = depth_anything.pretrained
        self.feat_decoder = depth_anything_decoder.depth_head

        
        # 🔥 新的多尺度特征增强器 - 直接在256维度上处理，节省显存
        self.multi_scale_feature_enhancer = nn.ModuleList([MultiScaleFeatureEnhancer(
            input_dim=256,       
            base_dim=64,        
            num_heads=4,        
            scales=[16, 8, 4],   
            use_checkpoint=False,
            rope_frequency=100
        ) for i in range(len(self.intermediate_layer_idx[self.args.encoder]))])  
       
        
        del depth_anything, depth_anything_decoder
        
        # self.update_coarse = BasicMultiUpdateBlock(self.args, hidden_dims=self.args.hidden_dims)
        # self.update_detail = UpdateBlock_Detail(self.args, hidden_dims=self.args.hidden_dims)
        # self.update_occ = UpdateBlock_Occ(self.args, hidden_dims=self.args.hidden_dims)
    
        # 🔥 新增：几何置信度相关参数（默认关闭，需要明确启用）
        self.use_geo_confidence = getattr(args, 'use_geo_confidence', True)  # 是否启用几何置信度
        self.geo_conf_window_size = getattr(args, 'geo_conf_window_size', 5)  # 局部窗口大小
        self.geo_conf_alpha = getattr(args, 'geo_conf_alpha', 0.7)  # 几何置信度的影响强度
        self.geo_conf_strategy = getattr(args, 'geo_conf_strategy', 'linear')  # 抑制策略
        self.geo_conf_min_weight = getattr(args, 'geo_conf_min_weight', 0.1)  # 最小权重
        
        
    def infer_mono(self, image1, image2):
        """单目深度推理和特征提取"""
        height_ori, width_ori = image1.shape[2:]
        
        # 统一的图像预处理
        resize_factor = 14 / 16
        resize_image1 = F.interpolate(image1, scale_factor=resize_factor, mode='bilinear', align_corners=True)
        resize_image2 = F.interpolate(image2, scale_factor=resize_factor, mode='bilinear', align_corners=True)

        patch_h, patch_w = resize_image1.shape[-2] // 14, resize_image1.shape[-1] // 14
        
        # 提取左图特征用于单目深度估计
        features_left_encoder = self.mono_encoder.get_intermediate_layers(
            resize_image1, self.intermediate_layer_idx[self.args.encoder], return_class_token=True
        )
        
        # 提取左右图特征用于立体匹配
        features_left = self.feat_decoder(features_left_encoder, patch_h, patch_w)
        features_right_encoder = self.mono_encoder.get_intermediate_layers(
            resize_image2, self.intermediate_layer_idx[self.args.encoder], return_class_token=True
        )
        features_right = self.feat_decoder(features_right_encoder, patch_h, patch_w)

        return features_left, features_right

    def freeze_bn(self):
        """冻结BatchNorm层"""
        for m in self.modules():
            if isinstance(m, nn.BatchNorm2d):
                m.eval()
            if isinstance(m, nn.SyncBatchNorm):
                m.eval()

    def upsample_disp(self, disp, mask_feat_4, stem_2x):
        """视差上采样"""
        xspx = self.spx_2_gru(mask_feat_4, stem_2x)
        spx_pred = self.spx_gru(xspx)
        spx_pred = F.softmax(spx_pred, 1)
        up_disp = context_upsample(disp*4., spx_pred).unsqueeze(1)
        return up_disp

    # def upsample_disp(self, disp, mask_feat_4, rgb):
    #     return self.enhanced_upsampler(disp, mask_feat_4, rgb)

    def hierarchical_disparity_estimation(self, match_left, match_right, features_left):
    
        gwc_volume = build_gwc_volume(match_left, match_right, 768//4, 8)

        gwc_volume = self.corr_stem(gwc_volume)
        gwc_volume = self.corr_feature_att(gwc_volume, features_left[0])
        geo_encoding_volume = self.cost_agg(gwc_volume, features_left)

        volume = self.classifier_coarse(geo_encoding_volume).squeeze(1)

        prob = F.softmax(volume, dim=1)
        init_disp = disparity_regression(prob, maxdisp=768//4)

        # occ_mask = self.occ_mask_head(volume)

        # 使用百分位数计算视差范围并取整
        min_disp = int(init_disp.min().item()-1)  
        max_disp = int(init_disp.max().item()+1)  
        
        # 确保范围有效性
        min_disp = max(0, min_disp)  # 确保最小视差不小于0
        max_disp = min(768//4 - 1, max_disp)  # 确保最大视差不超过原始范围
        if max_disp <= min_disp:  # 防止范围无效
            max_disp = min_disp + 8

        geo_encoding_volume_detail = geo_encoding_volume[:, :, min_disp:max_disp+1, :, :]
        volume = self.classifier_fine(geo_encoding_volume_detail).squeeze(1)
        prob = F.softmax(volume, dim=1)
        fine_disp = disparity_regression(prob, mindisp=min_disp, maxdisp=max_disp+1)

        return 0.1 * init_disp + 0.9 * fine_disp, prob, geo_encoding_volume

    def forward(self, image1, image2, iters=32, flow_init=None, test_mode=False, use_cuda_sampler=False):
        # 图像预处理
        image1 = (2 * (image1 / 255.0) - 1.0).contiguous()
        image2 = (2 * (image2 / 255.0) - 1.0).contiguous()
        
        features_mono_left, features_mono_right = self.infer_mono(image1, image2)

        # 多尺度特征增强
        features_mono_left = list(features_mono_left)
        features_mono_right = list(features_mono_right)
        features_mono_left, features_mono_right = zip(*[
            self.multi_scale_feature_enhancer[i](features_mono_left[i], features_mono_right[i])
            for i in range(len(features_mono_left))
        ])

        features_left = self.feat_transfer(features_mono_left)
        features_right = self.feat_transfer(features_mono_right)

        # 优化stem特征提取，避免重复计算
        stem_2x = self.stem_2(image1)
        stem_4x = self.stem_4(stem_2x)
        stem_8x = self.stem_8(stem_4x)
        stem_16x = self.stem_16(stem_8x)
        
        # 只为右图计算需要的stem特征
        stem_4y = self.stem_4(self.stem_2(image2))

        stem_x_list = [stem_16x, stem_8x, stem_4x]
        features_left[0] = torch.cat((features_left[0], stem_4x), 1)
        features_right[0] = torch.cat((features_right[0], stem_4y), 1)

        # 分层自适应立体匹配初始化
        match_left = self.desc(self.conv(features_left[0]))
        match_right = self.desc(self.conv(features_right[0]))
                
        # 使用增强后的特征进行分层自适应视差估计
        init_disp, prob, geo_encoding_volume = self.hierarchical_disparity_estimation(match_left, match_right, features_left)

        # if not test_mode:
        xspx = self.spx_4(features_left[0])
        xspx = self.spx_2(xspx, stem_2x)  
        spx_pred = self.spx(xspx)
        spx_pred = F.softmax(spx_pred, 1)

        # 上下文网络初始化
        cnet_list = self.feat_transfer_cnet(features_mono_left, stem_x_list)
        net_list = [torch.tanh(x) for x in cnet_list]
        inp_list = [torch.relu(x) for x in cnet_list]
        net_list_detail = [x.clone() for x in net_list]


        inp_list = [list(conv(i).split(split_size=conv.out_channels//3, dim=1)) 
                   for i,conv in zip(inp_list, self.context_zqr_convs)]

        geo_block = OptimizedCombinedGeoEncoding
        geo_fn = geo_block(match_left.float(), match_right.float(), geo_encoding_volume.float(), 
                           radius=self.args.corr_radius, num_levels=self.args.corr_levels)

        b, c, h, w = match_left.shape
        coords = torch.arange(w, device=match_left.device).float().reshape(1,1,w,1).repeat(b, h, 1, 1).contiguous()
        
        disp = init_disp
        # occ_mask = init_occ_mask
       
        disp_preds = []
        disp_occ_preds = []
        occ_mask_preds = []

        # 🔥 自适应切换变量
        switched_to_detail = False
        convergence_threshold = self.args.corr_radius//2  # 使用corr_radius作为收敛阈值
        convergence_cnt = 0
        
        # 新增：实例化遮挡检测模块
        attn_map = chunked_by_neighbor_similarity(features_mono_left[0])
        # mask_feat = self.context_conv(features_mono_left[0])
        # init_disp_up = self.upsample_disp(init_disp, mask_feat.detach(), stem_2x.detach())

        # occ_mask = None  # 新增：细节阶段遮挡图缓存
        for itr in range(iters):
            disp = disp.detach()

            if not switched_to_detail:
                geo_feat = geo_fn(disp, coords, level=self.args.corr_levels)
                net_list, delta_disp, mask_feat_4 = self.update_coarse(net_list, inp_list, geo_feat, disp, iter16=self.args.n_gru_layers==3, iter08=self.args.n_gru_layers>=2)                
                disp = disp + delta_disp

                convergence_cnt = convergence_cnt + 1 if delta_disp.abs().max() < convergence_threshold else 0
                switched_to_detail = True if convergence_cnt >= 3 else False
            
                if test_mode and itr < iters-1:
                    continue
                disp_up = self.upsample_disp(disp, mask_feat_4, stem_2x)
                disp_preds.append(disp_up)
            else:
                geo_feat = geo_fn(disp, coords, level=2)    
                warped_right = disp_warp(features_right[0], disp.detach())[0]
                mono_flaw = warped_right - features_left[0]

                net_list_detail, delta_disp, mask_feat_4 = self.update_detail(
                    net_list_detail, inp_list, disp, geo_feat, mono_flaw, attn_map,
                    iter16=self.args.n_gru_layers==3, iter08=self.args.n_gru_layers>=2)
                disp = disp + delta_disp
                if test_mode and itr < iters-1:
                    continue
                disp_up = self.upsample_disp(disp, mask_feat_4, stem_2x) 
                disp_preds.append(disp_up)

        if test_mode:
            disp_up = self.upsample_disp(disp, mask_feat_4, stem_2x)
            init_disp_up = context_upsample(init_disp*4., spx_pred.float()).unsqueeze(1)
            return disp_up, init_disp_up #, occ_mask
        
        init_disp_up = context_upsample(init_disp*4., spx_pred.float()).unsqueeze(1)
        return init_disp_up, disp_preds, prob #, init_occ_mask
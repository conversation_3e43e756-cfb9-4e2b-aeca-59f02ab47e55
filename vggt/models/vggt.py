# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
#
# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.

import torch
import torch.nn as nn
from huggingface_hub import PyTorchModelHubMixin  # used for model hub
from typing import List

from .aggregator import Aggregator
from ..heads.camera_head import CameraHead
from ..heads.dpt_head import DPTHead
import torch.nn.functional as F
# from vggt.heads.track_head import TrackHead  # 注释掉未使用的track_head


class ResidualConvUnit(nn.Module):
    """残差卷积单元"""
    def __init__(self, features: int):
        super().__init__()
        self.conv1 = nn.Conv2d(features, features, kernel_size=3, stride=1, padding=1, bias=True)
        self.conv2 = nn.Conv2d(features, features, kernel_size=3, stride=1, padding=1, bias=True)
        self.activation = nn.ReLU(inplace=True)

    def forward(self, x):
        out = self.activation(x)
        out = self.conv1(out)
        out = self.activation(out)
        out = self.conv2(out)
        return out + x


class FeatureFusionBlock(nn.Module):
    """简化的特征融合块"""
    def __init__(self, features: int, has_residual: bool = True):
        super().__init__()
        self.has_residual = has_residual

        if has_residual:
            self.resConfUnit1 = ResidualConvUnit(features)
        self.resConfUnit2 = ResidualConvUnit(features)

        self.out_conv = nn.Conv2d(features, features, kernel_size=1, stride=1, padding=0, bias=True)

    def forward(self, *xs, size=None):
        output = xs[0]

        if self.has_residual and len(xs) > 1:
            res = self.resConfUnit1(xs[1])
            output = output + res

        output = self.resConfUnit2(output)

        # 插值到目标尺寸
        if size is not None:
            output = F.interpolate(output, size=size, mode="bilinear", align_corners=True)

        output = self.out_conv(output)
        return output


class MonsterStyleDPTHead(nn.Module):
    """
    Monster Plus风格的DPT Head，专门用于生成精确的1/4, 1/8, 1/16, 1/32特征图
    参考Monster Plus中的DepthAnythingV2_decoder实现
    """
    def __init__(
        self,
        dim_in: int,
        patch_size: int = 14,
        features: int = 256,
        out_channels: List[int] = [256, 512, 1024, 1024],
        pos_embed: bool = True,
    ) -> None:
        super(MonsterStyleDPTHead, self).__init__()

        self.patch_size = patch_size
        self.pos_embed = pos_embed
        # self.intermediate_layer_idx = intermediate_layer_idx
        self.out_channels = out_channels

        self.norm = nn.LayerNorm(dim_in)

        # 投影层：将输入特征投影到不同的通道数
        self.projects = nn.ModuleList([
            nn.Conv2d(
                in_channels=dim_in,
                out_channels=oc,
                kernel_size=1,
                stride=1,
                padding=0,
            )
            for oc in out_channels
        ])

        # 关键：Monster Plus风格的resize_layers
        # 这些层确保不同层的特征图有正确的尺度关系
        self.resize_layers = nn.ModuleList([
            # Layer 0: 上采样4倍 (patch_size -> patch_size*4)
            nn.ConvTranspose2d(
                in_channels=out_channels[0],
                out_channels=out_channels[0],
                kernel_size=4, stride=4, padding=0
            ),
            # Layer 1: 上采样2倍 (patch_size -> patch_size*2)
            nn.ConvTranspose2d(
                in_channels=out_channels[1],
                out_channels=out_channels[1],
                kernel_size=2, stride=2, padding=0
            ),
            # Layer 2: 保持原尺寸 (patch_size -> patch_size)
            nn.Identity(),
            # Layer 3: 下采样2倍 (patch_size -> patch_size/2)
            nn.Conv2d(
                in_channels=out_channels[3],
                out_channels=out_channels[3],
                kernel_size=3, stride=2, padding=1
            ),
        ])

        # Scratch模块用于特征融合
        self.scratch = self._make_scratch(out_channels, features)

    def _make_scratch(self, in_shape: List[int], out_shape: int) -> nn.Module:
        """创建scratch模块"""
        class ScratchModule(nn.Module):
            def __init__(self):
                super().__init__()
                self.layer1_rn = nn.Conv2d(in_shape[0], out_shape, kernel_size=3, stride=1, padding=1, bias=False)
                self.layer2_rn = nn.Conv2d(in_shape[1], out_shape, kernel_size=3, stride=1, padding=1, bias=False)
                self.layer3_rn = nn.Conv2d(in_shape[2], out_shape, kernel_size=3, stride=1, padding=1, bias=False)
                self.layer4_rn = nn.Conv2d(in_shape[3], out_shape, kernel_size=3, stride=1, padding=1, bias=False)

                # 添加特征融合模块
                self.refinenet1 = FeatureFusionBlock(out_shape, has_residual=True)
                self.refinenet2 = FeatureFusionBlock(out_shape, has_residual=True)
                self.refinenet3 = FeatureFusionBlock(out_shape, has_residual=True)
                self.refinenet4 = FeatureFusionBlock(out_shape, has_residual=False)

        return ScratchModule()

    def _make_fusion_block(self, features: int, has_residual: bool = True) -> nn.Module:
        """创建特征融合块"""
        return FeatureFusionBlock(features, has_residual=has_residual)

    def forward(
        self,
        aggregated_tokens_list: List[torch.Tensor],
        images: torch.Tensor,
        patch_start_idx: int,
    ) -> List[torch.Tensor]:
        """
        Monster Plus风格的前向传播，生成精确的1/4, 1/8, 1/16, 1/32特征图

        Args:
            aggregated_tokens_list: 来自不同transformer层的token列表
            images: 输入图像 [B, S, 3, H, W]
            patch_start_idx: patch token的起始索引

        Returns:
            List[Tensor]: 4个尺度的特征图
                - feat_1_4: [B, S, 256, H/4, W/4]
                - feat_1_8: [B, S, 256, H/8, W/8]
                - feat_1_16: [B, S, 256, H/16, W/16]
                - feat_1_32: [B, S, 256, H/32, W/32]
        """
        B, S_chunk, _, H, W = images.shape
        patch_h, patch_w = H // self.patch_size, W // self.patch_size

        # 处理每个中间层的特征
        out = []
        for i in range(len(aggregated_tokens_list)):
            # 提取patch tokens
            x = aggregated_tokens_list[i][:, :, patch_start_idx:]

            # 重塑为 [B * S_chunk, P, 2*C]
            x = x.view(B * S_chunk, -1, x.shape[-1])

            # 归一化
            x = self.norm(x)

            # 重塑为2D特征图 [B * S_chunk, 2*C, patch_h, patch_w]
            x = x.permute(0, 2, 1).reshape((x.shape[0], x.shape[-1], patch_h, patch_w))

            # 投影到目标通道数
            x = self.projects[i](x)

            # 应用resize层以获得正确的尺度
            x = self.resize_layers[i](x)

            out.append(x)

        # 按照Monster Plus的方式进行特征融合
        layer_1, layer_2, layer_3, layer_4 = out

        # 通过1x1卷积调整通道数到统一的256维
        layer_1_rn = self.scratch.layer1_rn(layer_1)
        layer_2_rn = self.scratch.layer2_rn(layer_2)
        layer_3_rn = self.scratch.layer3_rn(layer_3)
        layer_4_rn = self.scratch.layer4_rn(layer_4)

        # 自上而下融合特征，生成多尺度特征金字塔
        # path_4: 最小尺度特征 (1/32)
        path_4 = self.scratch.refinenet4(layer_4_rn, size=layer_4_rn.shape[2:])

        # path_3: 上采样path_4并与layer_3_rn融合 (1/16)
        up_path_4 = F.interpolate(path_4, size=layer_3_rn.shape[2:], mode='bilinear', align_corners=True)
        path_3 = self.scratch.refinenet3(up_path_4, layer_3_rn, size=layer_3_rn.shape[2:])

        # path_2: 上采样path_3并与layer_2_rn融合 (1/8)
        up_path_3 = F.interpolate(path_3, size=layer_2_rn.shape[2:], mode='bilinear', align_corners=True)
        path_2 = self.scratch.refinenet2(up_path_3, layer_2_rn, size=layer_2_rn.shape[2:])

        # path_1: 上采样path_2并与layer_1_rn融合 (1/4)
        up_path_2 = F.interpolate(path_2, size=layer_1_rn.shape[2:], mode='bilinear', align_corners=True)
        path_1 = self.scratch.refinenet1(up_path_2, layer_1_rn, size=layer_1_rn.shape[2:])

        # 将特征图重塑为最终输出格式 [B, S_chunk, C, H, W]
        pyramid_features = []
        for path in [path_1, path_2, path_3, path_4]:
            C, H_scale, W_scale = path.shape[1], path.shape[2], path.shape[3]
            path = path.view(B, S_chunk, C, H_scale, W_scale)
            pyramid_features.append(path)

        return pyramid_features


class SimplifiedDPTHead(nn.Module):
    """
    简化的DPT头部，只生成1/4尺度的特征图
    """
    def __init__(
        self,
        dim_in: int,
        patch_size: int = 14,
        features: int = 256,
    ) -> None:
        super(SimplifiedDPTHead, self).__init__()

        self.patch_size = patch_size
        self.norm = nn.LayerNorm(dim_in)

        # 投影层：将输入特征投影到目标通道数
        self.project = nn.Conv2d(
            in_channels=dim_in,
            out_channels=features,
            kernel_size=1,
            stride=1,
            padding=0,
        )

        # 上采样层：从patch_size尺度上采样到1/4尺度
        # 对于patch_size=14，需要上采样约3.5倍到达1/4尺度
        self.upsample = nn.ConvTranspose2d(
            in_channels=features,
            out_channels=features,
            kernel_size=4, stride=4, padding=0
        )

        # 最终的精细化卷积
        self.refine = nn.Sequential(
            nn.Conv2d(features, features, 3, padding=1),
            nn.BatchNorm2d(features),
            nn.ReLU(inplace=True),
            nn.Conv2d(features, features, 3, padding=1),
            nn.BatchNorm2d(features),
            nn.ReLU(inplace=True)
        )

    def forward(
        self,
        dinov2_features: torch.Tensor,
        images: torch.Tensor,
        patch_start_idx: int,
    ) -> torch.Tensor:
        """
        Args:
            dinov2_features: DINOV2特征 [B*S, P, C]
            images: 输入图像 [B, S, 3, H, W]
            patch_start_idx: patch token的起始索引

        Returns:
            feat_1_4: 1/4尺度特征图 [B, S, 256, H/4, W/4]
        """
        B, S, _, H, W = images.shape
        patch_h, patch_w = H // self.patch_size, W // self.patch_size

        # 提取patch tokens，跳过特殊tokens
        x = dinov2_features[:, patch_start_idx:]  # [B*S, patch_h*patch_w, C]

        # 归一化
        x = self.norm(x)

        # 重塑为2D特征图
        x = x.permute(0, 2, 1).reshape(B * S, -1, patch_h, patch_w)  # [B*S, C, patch_h, patch_w]

        # 投影到目标通道数
        x = self.project(x)  # [B*S, 256, patch_h, patch_w]

        # 上采样到1/4尺度
        x = self.upsample(x)  # [B*S, 256, H/4, W/4]

        # 精细化
        x = self.refine(x)  # [B*S, 256, H/4, W/4]

        # 重塑为最终输出格式
        _, C, H_quarter, W_quarter = x.shape
        x = x.view(B, S, C, H_quarter, W_quarter)

        return x


class VGGT(nn.Module, PyTorchModelHubMixin):
    def __init__(self, img_size=512, patch_size=14, embed_dim=1024, intermediate_layer_idx=[4, 8, 12, 15]):
        super().__init__()

        self.aggregator = Aggregator(img_size=img_size, patch_size=patch_size, embed_dim=embed_dim, intermediate_layer_idx=intermediate_layer_idx)

        # 使用Monster Plus风格的特征头，确保生成精确的1/4, 1/8, 1/16, 1/32特征图
        self.feature_head = MonsterStyleDPTHead(
            dim_in=2 * embed_dim,
            patch_size=patch_size,
            features=256,
            out_channels=[256, 512, 1024, 1024],  # Monster Plus风格的通道配置
            pos_embed=True
        )

        # 新增：简化的DPT头部，只用DINOV2特征生成1/4尺度特征图
        self.simplified_head = SimplifiedDPTHead(
            dim_in=embed_dim,  # 只使用DINOV2特征，不是拼接的
            patch_size=patch_size,
            features=256,
        )

    def forward(
        self,
        images: torch.Tensor,
        use_simplified_head: bool = False,
    ):
        """
        Forward pass of the VGGT model.

        Args:
            images (torch.Tensor): Input images with shape [S, 3, H, W] or [B, S, 3, H, W], in range [0, 1].
                B: batch size, S: sequence length, 3: RGB channels, H: height, W: width
            use_simplified_head (bool): If True, use simplified head that only generates 1/4 scale features
                from DINOV2 without attention mechanisms. Default: False

        Returns:
            If use_simplified_head=False:
                List[Tensor]: 4个尺度的特征图
                    - feat_1_4: [B, S, 256, H/4, W/4]
                    - feat_1_8: [B, S, 256, H/8, W/8]
                    - feat_1_16: [B, S, 256, H/16, W/16]
                    - feat_1_32: [B, S, 256, H/32, W/32]

            If use_simplified_head=True:
                Tensor: 1/4尺度特征图
                    - feat_1_4: [B, S, 256, H/4, W/4]
        """

        # If without batch dimension, add it
        # 输入: images [S, 3, H, W] 或 [B, S, 3, H, W] -> 输出: images [B, S, 3, H, W]
        if len(images.shape) == 4:
            images = images.unsqueeze(0)

        # 统一的图像预处理
        resize_factor = 14 / 16
        # 保存原始形状
        B, S, C, H, W = images.shape
        # 重塑为 [B*S, C, H, W] 以便进行插值
        images = images.view(B * S, C, H, W)
        # 进行插值
        images = F.interpolate(images, scale_factor=resize_factor, mode='bilinear', align_corners=True)
        # 重新 reshape 回 [B, S, C, H', W']
        _, _, H_new, W_new = images.shape
        images = images.view(B, S, C, H_new, W_new)

        if use_simplified_head:
            # 简化模式：只使用DINOV2特征，跳过注意力机制
            # 直接从aggregator获取DINOV2特征
            dinov2_features, patch_start_idx = self._extract_dinov2_features(images)

            # 使用简化头部生成1/4尺度特征图
            feat_1_4 = self.simplified_head(dinov2_features, images, patch_start_idx)
            return feat_1_4
        else:
            # 完整模式：使用注意力机制和多尺度特征头
            # 调用Aggregator处理图像，提取多尺度特征
            # aggregated_tokens_list: List[torch.Tensor]，每个元素形状为 [B, S*P, 2*C]
            # patch_start_idx: int，patch tokens在token序列中的起始索引
            aggregated_tokens_list, patch_start_idx = self.aggregator(images)

            multi_scale_features = self.feature_head(
                aggregated_tokens_list, images=images, patch_start_idx=patch_start_idx
            )

            return multi_scale_features

    def _extract_dinov2_features(self, images: torch.Tensor):
        """
        直接提取DINOV2特征，跳过注意力机制

        Args:
            images: [B, S, 3, H, W]

        Returns:
            dinov2_features: [B*S, P, C] DINOV2特征
            patch_start_idx: int patch token起始索引
        """
        from .aggregator import slice_expand_and_flatten

        B, S, C_in, H, W = images.shape

        if C_in != 3:
            raise ValueError(f"Expected 3 input channels, got {C_in}")

        # 图像归一化
        _RESNET_MEAN = [0.485, 0.456, 0.406]
        _RESNET_STD = [0.229, 0.224, 0.225]
        resnet_mean = torch.FloatTensor(_RESNET_MEAN).view(1, 1, 3, 1, 1).to(images.device)
        resnet_std = torch.FloatTensor(_RESNET_STD).view(1, 1, 3, 1, 1).to(images.device)
        images = (images - resnet_mean) / resnet_std

        # 重塑为 [B*S, 3, H, W] 进行patch embedding
        images = images.view(B * S, C_in, H, W)

        # 直接使用DINOV2提取特征
        patch_tokens = self.aggregator.patch_embed(images)

        if isinstance(patch_tokens, dict):
            patch_tokens = patch_tokens["x_norm_patchtokens"]

        # patch_tokens: [B*S, P - patch_start_idx, C]
        _, _, C = patch_tokens.shape

        # 扩展camera和register tokens
        camera_token = slice_expand_and_flatten(self.aggregator.camera_token, B, S)
        register_token = slice_expand_and_flatten(self.aggregator.register_token, B, S)

        # 拼接特殊tokens和patch tokens
        tokens = torch.cat([camera_token, register_token, patch_tokens], dim=1)

        return tokens, self.aggregator.patch_start_idx
    
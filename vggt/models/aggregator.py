# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.
#
# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.

import logging
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple, Union, List, Dict, Any

from ..layers import PatchEmbed
from ..layers.block import Block
from ..layers.rope import RotaryPositionEmbedding2D, PositionGetter
from ..layers.vision_transformer import vit_small, vit_base, vit_large, vit_giant2

logger = logging.getLogger(__name__)

_RESNET_MEAN = [0.485, 0.456, 0.406]
_RESNET_STD = [0.229, 0.224, 0.225]


class Aggregator(nn.Module):
    

    def __init__(
        self,
        img_size=518,
        patch_size=14,
        embed_dim=1024,
        depth=16,
        num_heads=16,
        mlp_ratio=4.0,
        num_register_tokens=4,
        block_fn=Block,
        qkv_bias=True,
        proj_bias=True,
        ffn_bias=True,
        patch_embed="dinov2_vitl14_reg",
        # patch_embed="conv",
        aa_order=["frame", "global"],
        aa_block_size=1,
        qk_norm=True,
        rope_freq=100,
        init_values=0.01,
        intermediate_layer_idx: List[int] = [4, 8, 12, 15],
    ):
        super().__init__()

        self.__build_patch_embed__(patch_embed, img_size, patch_size, num_register_tokens, embed_dim=embed_dim)

        # Initialize rotary position embedding if frequency > 0
        self.rope = RotaryPositionEmbedding2D(frequency=rope_freq) if rope_freq > 0 else None
        self.position_getter = PositionGetter() if self.rope is not None else None

        self.frame_blocks = nn.ModuleList(
            [
                block_fn(
                    dim=embed_dim,
                    num_heads=num_heads,
                    mlp_ratio=mlp_ratio,
                    qkv_bias=qkv_bias,
                    proj_bias=proj_bias,
                    ffn_bias=ffn_bias,
                    init_values=init_values,
                    qk_norm=qk_norm,
                    rope=self.rope,
                )
                for _ in range(depth)
            ]
        )

        self.global_blocks = nn.ModuleList(
            [
                block_fn(
                    dim=embed_dim,
                    num_heads=num_heads,
                    mlp_ratio=mlp_ratio,
                    qkv_bias=qkv_bias,
                    proj_bias=proj_bias,
                    ffn_bias=ffn_bias,
                    init_values=init_values,
                    qk_norm=qk_norm,
                    rope=self.rope,
                )
                for _ in range(depth)
            ]
        )

        self.depth = depth
        self.aa_order = aa_order
        self.patch_size = patch_size
        self.aa_block_size = aa_block_size

        # Validate that depth is divisible by aa_block_size
        if self.depth % self.aa_block_size != 0:
            raise ValueError(f"depth ({depth}) must be divisible by aa_block_size ({aa_block_size})")

        self.aa_block_num = self.depth // self.aa_block_size
        self.intermediate_layer_idx = intermediate_layer_idx

        # Note: We have two camera tokens, one for the first frame and one for the rest
        # The same applies for register tokens
        # camera_token: [1, 2, 1, embed_dim]
        self.camera_token = nn.Parameter(torch.randn(1, 2, 1, embed_dim))
        # register_token: [1, 2, num_register_tokens, embed_dim]
        self.register_token = nn.Parameter(torch.randn(1, 2, num_register_tokens, embed_dim))

        # The patch tokens start after the camera and register tokens
        self.patch_start_idx = 1 + num_register_tokens

        # Initialize parameters with small values
        nn.init.normal_(self.camera_token, std=1e-6)
        nn.init.normal_(self.register_token, std=1e-6)

        # Register normalization constants as buffers
        for name, value in (
            ("_resnet_mean", _RESNET_MEAN),
            ("_resnet_std", _RESNET_STD),
        ):
            self.register_buffer(
                name,
                torch.FloatTensor(value).view(1, 1, 3, 1, 1),
                persistent=False,
            )

    def __build_patch_embed__(
        self,
        patch_embed,
        img_size,
        patch_size,
        num_register_tokens,
        interpolate_antialias=True,
        interpolate_offset=0.0,
        block_chunks=0,
        init_values=1.0,
        embed_dim=1024,
    ):
        """
        Build the patch embed layer. If 'conv', we use a
        simple PatchEmbed conv layer. Otherwise, we use a vision transformer.
        """

        if "conv" in patch_embed:
            self.patch_embed = PatchEmbed(img_size=img_size, patch_size=patch_size, in_chans=3, embed_dim=embed_dim)
        else:
            vit_models = {
                "dinov2_vitl14_reg": vit_large,
                "dinov2_vitb14_reg": vit_base,
                "dinov2_vits14_reg": vit_small,
                "dinov2_vitg2_reg": vit_giant2,
            }

            self.patch_embed = vit_models[patch_embed](
                img_size=img_size,
                patch_size=patch_size,
                num_register_tokens=num_register_tokens,
                interpolate_antialias=interpolate_antialias,
                interpolate_offset=interpolate_offset,
                block_chunks=block_chunks,
                init_values=init_values,
            )

            # Disable gradient updates for mask token
            if hasattr(self.patch_embed, "mask_token"):
                self.patch_embed.mask_token.requires_grad_(False)

    def forward(
        self,
        images: torch.Tensor,
    ) -> Tuple[List[torch.Tensor], int]:
        """
        Args:
            images (torch.Tensor): Input images with shape [B, S, 3, H, W], in range [0, 1].
                B: batch size, S: sequence length, 3: RGB channels, H: height, W: width

        Returns:
            (list[torch.Tensor], int):
                The list of outputs from the attention blocks,
                and the patch_start_idx indicating where patch tokens begin.
                
                Each element in the output list has shape [B, S, P, 2*C]
                where P is the number of tokens and C is the embedding dimension.
        """
        # images: [B, S, 3, H, W]
        B, S, C_in, H, W = images.shape

        if C_in != 3:
            raise ValueError(f"Expected 3 input channels, got {C_in}")

        # Normalize images and reshape for patch embed
        # images: [B, S, 3, H, W]
        images = (images - self._resnet_mean) / self._resnet_std

        # Reshape to [B*S, C, H, W] for patch embedding
        # images: [B*S, 3, H, W]
        images = images.view(B * S, C_in, H, W)
        # patch_tokens: [B*S, P - patch_start_idx, C] 其中P是总token数
        # with torch.no_grad():
        patch_tokens = self.patch_embed(images)

        if isinstance(patch_tokens, dict):
            patch_tokens = patch_tokens["x_norm_patchtokens"]

        # patch_tokens: [B*S, P - patch_start_idx, C]
        _, P_minus_start, C = patch_tokens.shape

        # Expand camera and register tokens to match batch size and sequence length
        # camera_token: [B*S, 1, C]
        camera_token = slice_expand_and_flatten(self.camera_token, B, S)
        # register_token: [B*S, num_register_tokens, C]
        register_token = slice_expand_and_flatten(self.register_token, B, S)

        # Concatenate special tokens with patch tokens
        # tokens: [B*S, P, C] 其中P = 1 + num_register_tokens + (P - patch_start_idx)
        tokens = torch.cat([camera_token, register_token, patch_tokens], dim=1)

        pos = None
        if self.rope is not None:
            # pos: [B*S, H//patch_size * W//patch_size, 2] = [B*S, P - patch_start_idx, 2]
            pos = self.position_getter(B * S, H // self.patch_size, W // self.patch_size, device=images.device)

        if self.patch_start_idx > 0:
            # do not use position embedding for special tokens (camera and register tokens)
            # so set pos to 0 for the special tokens
            pos = pos + 1
            # pos_special: [B*S, patch_start_idx, 2]
            pos_special = torch.zeros(B * S, self.patch_start_idx, 2).to(images.device).to(pos.dtype)
            # pos: [B*S, P, 2]
            pos = torch.cat([pos_special, pos], dim=1)

        # update P because we added special tokens
        # tokens: [B*S, P, C]
        _, P, C = tokens.shape

        frame_idx = 0
        global_idx = 0
        output_list = []

        # 处理交替注意力块
        for _ in range(self.aa_block_num):
            for attn_type in self.aa_order:
                if attn_type == "frame":
                    # 处理帧内注意力
                    tokens, frame_idx, frame_intermediates = self._process_frame_attention(
                        tokens, B, S, P, C, frame_idx, pos=pos
                    )
                elif attn_type == "global":
                    # 处理全局注意力（立体匹配中的条带注意力）
                    tokens, global_idx, global_intermediates = self._process_global_attention(
                        tokens, B, S, P, C, global_idx, H, W, pos=pos
                    )
                else:
                    raise ValueError(f"Unknown attention type: {attn_type}")

            # 将帧注意力和全局注意力的中间结果连接
            # frame_intermediates[i]: [B, S, P, C]
            # global_intermediates[i]: [B, S, P, C]
            # concat_inter: [B, S, P, 2*C]
            for i in range(len(frame_intermediates)):
                # Check if this layer should be included in the output
                current_layer_idx = frame_idx - len(frame_intermediates) + i
                if current_layer_idx in self.intermediate_layer_idx:
                    # concat frame and global intermediates, [B x S x P x 2C]
                    concat_inter = torch.cat([frame_intermediates[i], global_intermediates[i]], dim=-1)
                    output_list.append(concat_inter)

        del concat_inter
        del frame_intermediates
        del global_intermediates
        # output_list: List[torch.Tensor]，每个元素形状为 [B, S, P, 2*C]
        return output_list, self.patch_start_idx

    def _process_frame_attention(self, tokens, B, S, P, C, frame_idx, pos=None):
        """
        Process frame attention blocks. We keep tokens in shape (B*S, P, C).
        
        Args:
            tokens: 输入tokens [B*S, P, C]
            B: 批处理大小
            S: 序列长度
            P: token数量
            C: 通道维度
            frame_idx: 当前帧块索引
            pos: 位置嵌入
            
        Returns:
            tokens: 处理后的tokens [B*S, P, C]
            frame_idx: 更新后的帧块索引
            intermediates: 中间结果列表，每个元素形状为 [B, S, P, C]
        """
        # If needed, reshape tokens or positions:
        # 确保tokens形状为 [B*S, P, C]
        if tokens.shape != (B * S, P, C):
            tokens = tokens.view(B, S, P, C).view(B * S, P, C)

        # 确保pos形状为 [B*S, P, 2]
        if pos is not None and pos.shape != (B * S, P, 2):
            pos = pos.view(B, S, P, 2).view(B * S, P, 2)

        intermediates = []

        # by default, self.aa_block_size=1, which processes one block at a time
        for _ in range(self.aa_block_size):
            # tokens: [B*S, P, C] -> [B*S, P, C]
            tokens = self.frame_blocks[frame_idx](tokens, pos=pos)
            frame_idx += 1
            # 中间结果形状: [B, S, P, C]
            intermediates.append(tokens.view(B, S, P, C))

        return tokens, frame_idx, intermediates
    def _process_global_attention(self, tokens, B, S, P, C, global_idx, H=None, W=None, pos=None):
        """
        Process stripe-based attention blocks. Divides height into 4 stripes for efficient attention computation.
        """
        # Calculate spatial dimensions
        if H is None or W is None:
            raise ValueError("Height (H) and Width (W) must be provided for stripe-based attention")

        # Calculate patch dimensions
        patch_H = H // self.patch_size
        patch_W = W // self.patch_size

        # Number of spatial tokens (excluding special tokens)
        spatial_tokens = patch_H * patch_W

        # Ensure we have the expected number of tokens
        if P != self.patch_start_idx + spatial_tokens:
            raise ValueError(f"Expected {self.patch_start_idx + spatial_tokens} tokens, got {P}")

        # Reshape tokens to (B, S, P, C) format
        if tokens.shape != (B, S, P, C):
            tokens = tokens.view(B, S, P, C)

        # Reshape positions to (B, S, P, 2) format
        if pos is not None and pos.shape != (B, S, P, 2):
            pos = pos.view(B, S, P, 2)

        # Separate special tokens from spatial tokens
        special_tokens = tokens[:, :, :self.patch_start_idx, :]  # [B, S, patch_start_idx, C]
        spatial_tokens_tensor = tokens[:, :, self.patch_start_idx:, :]  # [B, S, spatial_tokens, C]

        if pos is not None:
            spatial_pos = pos[:, :, self.patch_start_idx:, :]  # [B, S, spatial_tokens, 2]

        # Reshape spatial tokens to image format: (B, S, patch_H, patch_W, C)
        spatial_tokens_img = spatial_tokens_tensor.view(B, S, patch_H, patch_W, C)
        if pos is not None:
            spatial_pos_img = spatial_pos.view(B, S, patch_H, patch_W, 2)

        # Divide height into 4 stripes: (B, S, 4, patch_H//4, patch_W, C)
        stripe_H = patch_H // 4
        if patch_H % 4 != 0:
            raise ValueError(f"Height {patch_H} must be divisible by 4 for stripe attention")

        spatial_tokens_stripes = spatial_tokens_img.view(B, S, 4, stripe_H, patch_W, C)
        if pos is not None:
            spatial_pos_stripes = spatial_pos_img.view(B, S, 4, stripe_H, patch_W, 2)

        # Reshape for efficient attention computation: (B*4, S * stripe_H * patch_W, C)
        stripe_tokens = spatial_tokens_stripes.permute(0, 2, 1, 3, 4, 5).reshape(B * 4, S * stripe_H * patch_W, C)

        if pos is not None:
            stripe_pos = spatial_pos_stripes.permute(0, 2, 1, 3, 4, 5).reshape(B * 4, S * stripe_H * patch_W, 2)
        else:
            stripe_pos = None

        intermediates = []

        # Process attention blocks for each stripe
        for _ in range(self.aa_block_size):
            # Apply attention to stripe tokens
            stripe_tokens = self.global_blocks[global_idx](stripe_tokens, pos=stripe_pos)
            global_idx += 1

            # Reshape back to spatial format
            # 合并view和permute操作，减少中间张量创建
            spatial_tokens_processed = stripe_tokens.view(B, 4, S, stripe_H, patch_W, C) \
                                                .permute(0, 2, 1, 3, 4, 5) \
                                                .reshape(B, S, patch_H, patch_W, C) \
                                                .view(B, S, spatial_tokens, C)

            # Concatenate special tokens back with processed spatial tokens
            processed_tokens = torch.cat([special_tokens, spatial_tokens_processed], dim=2)  # [B, S, P, C]
            intermediates.append(processed_tokens)

            # Update stripe_tokens for next iteration if needed
            if _ < self.aa_block_size - 1:  # 只有在不是最后一次迭代时才需要更新
                stripe_tokens = spatial_tokens_processed.view(B, S, patch_H, patch_W, C) \
                                          .permute(0, 2, 1, 3, 4, 5) \
                                          .reshape(B * 4, S * stripe_H * patch_W, C)

        # Final output should be in the expected format for the caller
        final_tokens = processed_tokens.view(B, S * P, C)

        return final_tokens, global_idx, intermediates

    # def _process_global_attention(self, tokens, B, S, P, C, global_idx, H=None, W=None, pos=None):
    #     """
    #     Process global attention blocks. We keep tokens in shape (B, S*P, C).
    #     """
    #     if tokens.shape != (B, S * P, C):
    #         tokens = tokens.view(B, S, P, C).view(B, S * P, C)

    #     if pos is not None and pos.shape != (B, S * P, 2):
    #         pos = pos.view(B, S, P, 2).view(B, S * P, 2)

    #     intermediates = []

    #     # by default, self.aa_block_size=1, which processes one block at a time
    #     for _ in range(self.aa_block_size):
    #         tokens = self.global_blocks[global_idx](tokens, pos=pos)
    #         global_idx += 1
    #         intermediates.append(tokens.view(B, S, P, C))

    #     return tokens, global_idx, intermediates
def slice_expand_and_flatten(token_tensor, B, S):
    """
    Processes specialized tokens with shape (1, 2, X, C) for multi-frame processing:
    1) Uses the first position (index=0) for the first frame only
    2) Uses the second position (index=1) for all remaining frames (S-1 frames)
    3) Expands both to match batch size B
    4) Concatenates to form (B, S, X, C) where each sequence has 1 first-position token
       followed by (S-1) second-position tokens
    5) Flattens to (B*S, X, C) for processing

    Args:
        token_tensor: 输入张量 [1, 2, X, C]
        B: batch size
        S: sequence length

    Returns:
        torch.Tensor: Processed tokens with shape (B*S, X, C)
    """

    # Slice out the "query" tokens => shape (1, 1, X, C)
    # query: [B, 1, X, C]
    query = token_tensor[:, 0:1, ...].expand(B, 1, *token_tensor.shape[2:])
    # Slice out the "other" tokens => shape (1, S-1, X, C)
    # others: [B, S-1, X, C]
    others = token_tensor[:, 1:, ...].expand(B, S - 1, *token_tensor.shape[2:])
    # Concatenate => shape (B, S, X, C)
    # combined: [B, S, X, C]
    combined = torch.cat([query, others], dim=1)

    # Finally flatten => shape (B*S, X, C)
    # combined: [B*S, X, C]
    combined = combined.view(B * S, *combined.shape[2:])
    return combined
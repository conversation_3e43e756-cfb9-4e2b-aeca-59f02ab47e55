import torch
import torch.nn.functional as F
import sys
sys.path.append('./core')

# 模拟simple_test.py中的测试数据
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
torch.manual_seed(42)
image1 = torch.randn(1, 3, 352, 768).to(device) 
image2 = torch.randn(1, 3, 352, 768).to(device)

print(f"测试数据形状: image1={image1.shape}, image2={image2.shape}")

# 导入Monster模型
from core.monster_orgin import Monster

# 创建参数
class Args:
    def __init__(self):
        self.encoder = 'vitl'
        self.max_disp = 768
        self.mixed_precision = False
        self.n_gru_layers = 3
        self.hidden_dims = [128, 128, 128]
        self.corr_levels = 2
        self.corr_radius = 4
        self.n_downsample = 2

args = Args()
model = Monster(args).to(device)
model.eval()

print("模型加载完成")

# 直接测试geometry模块
print("开始测试geometry模块...")

# 获取模型的一些中间输出用于测试geometry模块
with torch.no_grad():
    # 获取特征
    print("获取特征...")
    image1_normalized = (2 * (image1 / 255.0) - 1.0).contiguous()
    image2_normalized = (2 * (image2 / 255.0) - 1.0).contiguous()
    
    with torch.autocast(device_type='cuda', dtype=torch.float32): 
        depth_mono, features_mono_left, features_mono_right = model.infer_mono(image1_normalized, image2_normalized)
    
    print(f"单目深度形状: {depth_mono.shape}")
    print(f"特征形状: left={len(features_mono_left)}, right={len(features_mono_right)}")
    
    scale_factor = 0.25
    size = (int(depth_mono.shape[-2] * scale_factor), int(depth_mono.shape[-1] * scale_factor))
    disp_mono_4x = F.interpolate(depth_mono, size=size, mode='bilinear', align_corners=False)
    print(f"缩放后单目深度形状: {disp_mono_4x.shape}")
    
    features_left = model.feat_transfer(features_mono_left)
    features_right = model.feat_transfer(features_mono_right)
    print(f"传输后特征形状: left={len(features_left)}, right={len(features_right)}")
    
    stem_2x = model.stem_2(image1_normalized)
    stem_4x = model.stem_4(stem_2x)
    stem_8x = model.stem_8(stem_4x)
    stem_16x = model.stem_16(stem_8x)
    stem_2y = model.stem_2(image2_normalized)
    stem_4y = model.stem_4(stem_2y)
    
    stem_x_list = [stem_16x, stem_8x, stem_4x]
    features_left[0] = torch.cat((features_left[0], stem_4x), 1)
    features_right[0] = torch.cat((features_right[0], stem_4y), 1)
    
    print(f"特征连接后形状: left[0]={features_left[0].shape}, right[0]={features_right[0].shape}")
    
    match_left = model.desc(model.conv(features_left[0]))
    match_right = model.desc(model.conv(features_right[0]))
    print(f"匹配特征形状: left={match_left.shape}, right={match_right.shape}")
    
    from core.submodule import build_gwc_volume
    
    gwc_volume = build_gwc_volume(match_left, match_right, args.max_disp//4, 8)
    gwc_volume = model.corr_stem(gwc_volume)
    gwc_volume = model.corr_feature_att(gwc_volume, features_left[0])
    geo_encoding_volume = model.cost_agg(gwc_volume, features_left)
    print(f"几何编码体积形状: {geo_encoding_volume.shape}")
    
    # Init disp from geometry encoding volume
    from core.submodule import disparity_regression
    prob = F.softmax(model.classifier(geo_encoding_volume).squeeze(1), dim=1)
    init_disp = disparity_regression(prob, maxdisp=args.max_disp//4)
    print(f"初始视差形状: {init_disp.shape}")
    
    b, c, h, w = match_left.shape
    coords = torch.arange(w).float().to(match_left.device).reshape(1,1,w,1).repeat(b, h, 1, 1).contiguous()
    
    print(f"b={b}, c={c}, h={h}, w={w}")
    print(f"coords形状: {coords.shape}")
    print(f"b*h*w={b*h*w}")
    print(f"init_disp元素总数: {init_disp.numel()}")
    print(f"预期reshape后形状元素数: {b*h*w * 1 * 1 * 1}")
    
    # 检查实际元素数量
    print(f"init_disp元素总数: {init_disp.numel()}")
    print(f"预期reshape后形状元素数: {b*h*w * 1 * 1 * 1}")
    
    # 尝试进行reshape操作
    try:
        reshaped_disp = init_disp.reshape(b*h*w, 1, 1, 1)
        print(f"reshape成功: {reshaped_disp.shape}")
    except Exception as e:
        print(f"reshape失败: {e}")
        
    # 测试geometry模块
    from core.geometry import Combined_Geo_Encoding_Volume
    geo_block = Combined_Geo_Encoding_Volume
    geo_fn = geo_block(match_left.float(), match_right.float(), geo_encoding_volume.float(), 
                       radius=args.corr_radius, num_levels=args.corr_levels)
    
    print("开始调用geometry函数...")
    try:
        geo_feat = geo_fn(init_disp, coords)
        print(f"Geometry特征计算成功，形状: {geo_feat.shape}")
    except Exception as e:
        print(f"Geometry特征计算失败: {e}")
        import traceback
        traceback.print_exc()
【wandb: {}
project_name: pretrain
restore_ckpt: checkpoints/pretrain/140000_plus.pth
logdir: ./checkpoints/pretrain/
encoder: vitl
batch_size: 12
train_datasets:
- pretrain
lr: 0.0003
wdecay: 1.0e-05
total_step: 200000
save_frequency: 2000
save_path: ./checkpoints/pretrain/
val_frequency: 5000
image_size:
- 384
- 768
train_iters: 8
valid_iters: 32
val_dataset: kitti
corr_implementation: reg
corr_levels: 2
corr_radius: 4
n_downsample: 2
n_gru_layers: 3
hidden_dims:
- 128
- 128
- 128
max_disp: 768
saturation_range:
- 0.7
- 1.3
do_flip: false
spatial_scale:
- -0.4
- 0.2
noyjitter: false
num_gpu: 4
seed: 666
enable_adaptive: true
adaptive_lambda_budget: 0.05
adaptive_gamma: 0.15
gamma: 0.8
progressive_taraining: true
progressive_warmup_steps: 5000
progressive_transition_steps: 10000

#!/usr/bin/env python3
"""
测试简化版VGGT模型的脚本
"""

import torch
import torch.nn.functional as F
from vggt.models.vggt import VGGT

def test_simplified_vggt():
    """测试简化版VGGT模型"""
    print("Testing Simplified VGGT...")
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 创建模型
    model = VGGT(
        img_size=512,
        patch_size=14,
        embed_dim=1024,
        intermediate_layer_idx=[4, 8, 12, 15]
    ).to(device)
    
    # 创建测试输入
    batch_size = 2
    sequence_length = 4
    height, width = 448, 448  # 使用14的倍数
    
    # 输入图像 [B, S, 3, H, W]
    images = torch.randn(batch_size, sequence_length, 3, height, width).to(device)
    print(f"Input images shape: {images.shape}")
    
    # 测试简化模式
    print("\n=== Testing Simplified Mode ===")
    with torch.no_grad():
        try:
            feat_1_4 = model(images, use_simplified_head=True)
            print(f"✅ Simplified mode successful!")
            print(f"Output feat_1_4 shape: {feat_1_4.shape}")
            
            # 验证输出尺寸
            expected_h = height // 4
            expected_w = width // 4
            expected_shape = (batch_size, sequence_length, 256, expected_h, expected_w)
            
            if feat_1_4.shape == expected_shape:
                print(f"✅ Output shape is correct: {feat_1_4.shape}")
            else:
                print(f"❌ Output shape mismatch. Expected: {expected_shape}, Got: {feat_1_4.shape}")
                
        except Exception as e:
            print(f"❌ Simplified mode failed: {e}")
            import traceback
            traceback.print_exc()
    
    # 测试完整模式（用于对比）
    print("\n=== Testing Full Mode ===")
    with torch.no_grad():
        try:
            multi_scale_features = model(images, use_simplified_head=False)
            print(f"✅ Full mode successful!")
            print(f"Number of multi-scale features: {len(multi_scale_features)}")
            for i, feat in enumerate(multi_scale_features):
                print(f"  Feature {i+1} shape: {feat.shape}")
                
        except Exception as e:
            print(f"❌ Full mode failed: {e}")
            import traceback
            traceback.print_exc()
    
    # 计算参数数量
    total_params = sum(p.numel() for p in model.parameters())
    simplified_head_params = sum(p.numel() for p in model.simplified_head.parameters())
    
    print(f"\n=== Model Statistics ===")
    print(f"Total parameters: {total_params:,}")
    print(f"Simplified head parameters: {simplified_head_params:,}")
    print(f"Simplified head ratio: {simplified_head_params/total_params*100:.2f}%")
    
    return model

def test_performance_comparison():
    """比较简化模式和完整模式的性能"""
    print("\n" + "="*60)
    print("Performance Comparison")
    print("="*60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = VGGT(img_size=512, patch_size=14, embed_dim=1024).to(device)
    
    # 测试输入
    images = torch.randn(1, 2, 3, 448, 448).to(device)
    
    # 预热
    with torch.no_grad():
        _ = model(images, use_simplified_head=True)
        _ = model(images, use_simplified_head=False)
    
    # 测试简化模式性能
    if device.type == 'cuda':
        torch.cuda.synchronize()
        
    import time
    
    # 简化模式
    start_time = time.time()
    with torch.no_grad():
        for _ in range(10):
            _ = model(images, use_simplified_head=True)
    if device.type == 'cuda':
        torch.cuda.synchronize()
    simplified_time = (time.time() - start_time) / 10
    
    # 完整模式
    start_time = time.time()
    with torch.no_grad():
        for _ in range(10):
            _ = model(images, use_simplified_head=False)
    if device.type == 'cuda':
        torch.cuda.synchronize()
    full_time = (time.time() - start_time) / 10
    
    print(f"Simplified mode average time: {simplified_time*1000:.2f} ms")
    print(f"Full mode average time: {full_time*1000:.2f} ms")
    print(f"Speedup: {full_time/simplified_time:.2f}x")

if __name__ == "__main__":
    # 运行测试
    model = test_simplified_vggt()
    
    # 性能比较
    test_performance_comparison()
    
    print("\n" + "="*60)
    print("✅ All tests completed!")
    print("="*60)
